{"name": "ggcasecatalogs", "version": "1.0.0", "description": "haiya back to native with webpack", "main": "index.js", "scripts": {"dev": "webpack serve --config webpack/webpack.dev.js", "build": "webpack --config webpack/webpack.prod.js", "build:dev": "webpack --config webpack/webpack.dev.js", "start:api": "nodemon api/index.js", "start": "concurrently \"npm run start:api\" \"npm run dev\"", "clean": "<PERSON><PERSON><PERSON> dist"}, "author": "GGCatalogTeam", "license": "ISC", "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-runtime": "^7.28.0", "@babel/preset-env": "^7.28.0", "babel-loader": "^10.0.0", "concurrently": "^9.2.0", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "webpack-merge": "^6.0.1"}, "dependencies": {"@babel/runtime": "^7.28.2", "axios": "^1.11.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "multer": "^2.0.2", "mysql2": "^3.14.2", "qrcode": "^1.5.4", "sharp": "^0.34.3", "socket.io": "^4.8.1"}}