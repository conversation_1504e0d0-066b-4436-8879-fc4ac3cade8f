const axios = require('axios');
const db = require('./db');
const bcrypt = require('bcrypt');

// Configuration
const BASE_URL = 'http://localhost:3000/api';
const ADMIN_CREDENTIALS = {
  username: 'adminUser',
  password: 'password'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Test data with unique names based on database schema
const timestamp = Date.now();
const testData = {
  // Brands table: id, name, brand_photo, created_at
  brand: {
    name: `Test Brand ${timestamp}`,
    brand_photo: 'https://example.com/test-brand.jpg'
  },

  // Categories table: id, name, category_photo, created_at
  category: {
    name: `Test Category ${timestamp}`,
    category_photo: 'https://example.com/test-category.jpg'
  },

  // Products table: id, name, description, brand_id, category_id, total_sold, avg_rating, total_raters, created_at, updated_at
  product: {
    name: `Test Product ${timestamp}`,
    description: 'Test product description for comprehensive testing',
    brand_id: null, // Will be set after brand creation
    category_id: null // Will be set after category creation
  },

  // Web_banners table: id, title, banner_image_url, redirect_url, active, created_at, updated_at
  banner: {
    title: `Test Banner ${timestamp}`,
    banner_image_url: 'https://example.com/test-banner.jpg',
    redirect_url: 'https://example.com/test-redirect',
    active: true
  },

  // Product_variants table: id, product_id, variant_name, price
  variant: {
    variant_name: `Test Variant ${timestamp}`,
    price: 99.99
  },

  // Product_photos table: id, product_id, photo_url
  photo: {
    photo_url: 'https://example.com/test-product-photo.jpg'
  },

  // Ratings table: id, product_id, star, review_text, created_at
  rating: {
    star: 5,
    review_text: `Test review ${timestamp} - This is a comprehensive test rating`
  },

  // Admins table: id, username, password_hash, created_at, updated_at
  admin: {
    username: `testadmin${timestamp}`,
    password_hash: 'test_password_hash' // Will be properly hashed in tests
  }
};

// Global variables for test tracking
let adminSessionCookie = '';
let testResults = { passed: 0, failed: 0, total: 0 };
let createdIds = {
  brand: null,
  category: null,
  product: null,
  banner: null,
  variant: null,
  photo: null,
  rating: null,
  admin: null
};

// Helper functions
const log = (message, color = colors.reset) => console.log(`${color}${message}${colors.reset}`);

const updateResults = (passed) => {
  testResults.total++;
  if (passed) testResults.passed++;
  else testResults.failed++;
};

const makeRequest = async (method, endpoint, data = null, requireAuth = false) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: { 'Content-Type': 'application/json' }
    };

    if (requireAuth && adminSessionCookie) {
      config.headers['Cookie'] = adminSessionCookie;
    }
    if (data) config.data = data;

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
};

// Simplified test functions
const testHealthCheck = async () => {
  const result = await makeRequest('GET', '/health');
  const passed = result.success;
  updateResults(passed);
  log(`Health Check: ${passed ? '✅ PASS' : '❌ FAIL'}`, passed ? colors.green : colors.red);
  return passed;
};

const testDatabaseConnection = async () => {
  const result = await makeRequest('GET', '/test-db');
  const passed = result.success;
  updateResults(passed);
  log(`Database Test: ${passed ? '✅ PASS' : '❌ FAIL'}`, passed ? colors.green : colors.red);
  return passed;
};

const testAdminLogin = async () => {
  try {
    const [rows] = await db.execute('SELECT * FROM admins WHERE username = ?', [ADMIN_CREDENTIALS.username]);
    if (rows.length > 0) {
      log('🔍 Admin user exists, checking password...');

      const admin = rows[0];
      const isValidPassword = await bcrypt.compare(ADMIN_CREDENTIALS.password, admin.password_hash);

      if (isValidPassword) {
        log('✅ Admin user and password are correct!');
        log(`Username: ${ADMIN_CREDENTIALS.username}`);
        log(`Password: ${ADMIN_CREDENTIALS.password}`);

      } else {
        log('❌ Password doesn\'t match. Updating admin user...');

        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(ADMIN_CREDENTIALS.password, saltRounds);

        await db.execute(
          'UPDATE admins SET password_hash = ? WHERE username = ?',
          [hashedPassword, ADMIN_CREDENTIALS.username]
        );

        log('Admin password updated successfully!');
        log(`Username: ${ADMIN_CREDENTIALS.username}`, colors.green);
        log(`Password: ${ADMIN_CREDENTIALS.password}`, colors.red);
      }

      const response = await axios.post(`${BASE_URL}/admin/login`, ADMIN_CREDENTIALS);
      adminSessionCookie = response.headers['set-cookie']?.[0] || '';
      const passed = response.status === 200 && !!adminSessionCookie;
      updateResults(passed);
      
      log(`Admin Login: ${passed ? '✅ PASS' : '❌ FAIL'}`, passed ? colors.green : colors.red);
      
      return passed;
    } else {
      log('❌ Admin user does not exist. Creating admin user...');
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(ADMIN_CREDENTIALS.password, saltRounds);
      await db.execute(
        'INSERT INTO admins (username, password_hash, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
        [ADMIN_CREDENTIALS.username, hashedPassword]
      );
      log('Admin user created successfully!');
      return await testAdminLogin();
    }
  } catch (error) {
    updateResults(false);
    log('Admin Login: ❌ FAIL', colors.red);
    return false;
  }
};

// ===== BRAND CRUD TESTS =====
const testBrandCRUD = async () => {
  log('🏷️  Testing Brand CRUD Operations...', colors.cyan);
  let allPassed = true;

  // CREATE Brand
  const createResult = await makeRequest('POST', '/brands', testData.brand, true);
  const createPassed = createResult.success;
  updateResults(createPassed);
  log(`  Create Brand: ${createPassed ? '✅ PASS' : '❌ FAIL'}`, createPassed ? colors.green : colors.red);
  if (createPassed) createdIds.brand = createResult.data.id;
  allPassed = allPassed && createPassed;

  // READ Brand (Get All)
  const getAllResult = await makeRequest('GET', '/brands');
  const getAllPassed = getAllResult.success;
  updateResults(getAllPassed);
  log(`  Get All Brands: ${getAllPassed ? '✅ PASS' : '❌ FAIL'}`, getAllPassed ? colors.green : colors.red);
  allPassed = allPassed && getAllPassed;

  // READ Brand (Get By ID)
  if (createdIds.brand) {
    const getByIdResult = await makeRequest('GET', `/brands/${createdIds.brand}`);
    const getByIdPassed = getByIdResult.success;
    updateResults(getByIdPassed);
    log(`  Get Brand By ID: ${getByIdPassed ? '✅ PASS' : '❌ FAIL'}`, getByIdPassed ? colors.green : colors.red);
    allPassed = allPassed && getByIdPassed;
  }

  // UPDATE Brand
  if (createdIds.brand) {
    const updateData = {
      name: `Updated ${testData.brand.name}`,
      brand_photo: 'https://example.com/updated-brand.jpg'
    };
    const updateResult = await makeRequest('PUT', `/brands/${createdIds.brand}`, updateData, true);
    const updatePassed = updateResult.success;
    updateResults(updatePassed);
    log(`  Update Brand: ${updatePassed ? '✅ PASS' : '❌ FAIL'}`, updatePassed ? colors.green : colors.red);
    allPassed = allPassed && updatePassed;
  }

  // DELETE Brand (will be done at the end to maintain referential integrity)

  return allPassed;
};

// ===== CATEGORY CRUD TESTS =====
const testCategoryCRUD = async () => {
  log('📂 Testing Category CRUD Operations...', colors.cyan);
  let allPassed = true;

  // CREATE Category
  const createResult = await makeRequest('POST', '/categories', testData.category, true);
  const createPassed = createResult.success;
  updateResults(createPassed);
  log(`  Create Category: ${createPassed ? '✅ PASS' : '❌ FAIL'}`, createPassed ? colors.green : colors.red);
  if (createPassed) createdIds.category = createResult.data.id;
  allPassed = allPassed && createPassed;

  // READ Category (Get All)
  const getAllResult = await makeRequest('GET', '/categories');
  const getAllPassed = getAllResult.success;
  updateResults(getAllPassed);
  log(`  Get All Categories: ${getAllPassed ? '✅ PASS' : '❌ FAIL'}`, getAllPassed ? colors.green : colors.red);
  allPassed = allPassed && getAllPassed;

  // READ Category (Get By ID)
  if (createdIds.category) {
    const getByIdResult = await makeRequest('GET', `/categories/${createdIds.category}`);
    const getByIdPassed = getByIdResult.success;
    updateResults(getByIdPassed);
    log(`  Get Category By ID: ${getByIdPassed ? '✅ PASS' : '❌ FAIL'}`, getByIdPassed ? colors.green : colors.red);
    allPassed = allPassed && getByIdPassed;
  }

  // UPDATE Category
  if (createdIds.category) {
    const updateData = {
      name: `Updated ${testData.category.name}`,
      category_photo: 'https://example.com/updated-category.jpg'
    };
    const updateResult = await makeRequest('PUT', `/categories/${createdIds.category}`, updateData, true);
    const updatePassed = updateResult.success;
    updateResults(updatePassed);
    log(`  Update Category: ${updatePassed ? '✅ PASS' : '❌ FAIL'}`, updatePassed ? colors.green : colors.red);
    allPassed = allPassed && updatePassed;
  }

  // DELETE Category (will be done at the end to maintain referential integrity)

  return allPassed;
};

// ===== PRODUCT CRUD TESTS =====
const testProductCRUD = async () => {
  log('📦 Testing Product CRUD Operations...', colors.cyan);
  let allPassed = true;

  // CREATE Product (using created brand and category IDs)
  const productData = {
    ...testData.product,
    brand_id: createdIds.brand,
    category_id: createdIds.category
  };

  const createResult = await makeRequest('POST', '/products', productData, true);
  const createPassed = createResult.success;
  updateResults(createPassed);
  log(`  Create Product: ${createPassed ? '✅ PASS' : '❌ FAIL'}`, createPassed ? colors.green : colors.red);
  if (createPassed) createdIds.product = createResult.data.id;
  allPassed = allPassed && createPassed;

  // READ Product (Get All)
  const getAllResult = await makeRequest('GET', '/products');
  const getAllPassed = getAllResult.success;
  updateResults(getAllPassed);
  log(`  Get All Products: ${getAllPassed ? '✅ PASS' : '❌ FAIL'}`, getAllPassed ? colors.green : colors.red);
  allPassed = allPassed && getAllPassed;

  // READ Product (Get By ID)
  if (createdIds.product) {
    const getByIdResult = await makeRequest('GET', `/products/${createdIds.product}`);
    const getByIdPassed = getByIdResult.success;
    updateResults(getByIdPassed);
    log(`  Get Product By ID: ${getByIdPassed ? '✅ PASS' : '❌ FAIL'}`, getByIdPassed ? colors.green : colors.red);
    allPassed = allPassed && getByIdPassed;
  }

  // UPDATE Product
  if (createdIds.product) {
    const updateData = {
      name: `Updated ${testData.product.name}`,
      description: 'Updated product description for comprehensive testing',
      brand_id: createdIds.brand,
      category_id: createdIds.category
    };
    const updateResult = await makeRequest('PUT', `/products/${createdIds.product}`, updateData, true);
    const updatePassed = updateResult.success;
    updateResults(updatePassed);
    log(`  Update Product: ${updatePassed ? '✅ PASS' : '❌ FAIL'}`, updatePassed ? colors.green : colors.red);
    allPassed = allPassed && updatePassed;
  }

  // DELETE Product (will be done at the end)

  return allPassed;
};

// ===== PRODUCT VARIANT CRUD TESTS =====
const testVariantCRUD = async () => {
  log('🔧 Testing Product Variant CRUD Operations...', colors.cyan);
  let allPassed = true;

  if (!createdIds.product) {
    log('  ⚠️  Skipping variant tests - no product created', colors.yellow);
    return false;
  }

  // CREATE Variant
  const createResult = await makeRequest('POST', `/products/${createdIds.product}/variants`, testData.variant, true);
  const createPassed = createResult.success;
  updateResults(createPassed);
  log(`  Create Variant: ${createPassed ? '✅ PASS' : '❌ FAIL'}`, createPassed ? colors.green : colors.red);
  if (createPassed) createdIds.variant = createResult.data.id;
  allPassed = allPassed && createPassed;

  // READ Variants (Get by Product ID)
  const getByProductResult = await makeRequest('GET', `/products/${createdIds.product}/variants`);
  const getByProductPassed = getByProductResult.success;
  updateResults(getByProductPassed);
  log(`  Get Variants by Product: ${getByProductPassed ? '✅ PASS' : '❌ FAIL'}`, getByProductPassed ? colors.green : colors.red);
  allPassed = allPassed && getByProductPassed;

  // UPDATE Variant
  if (createdIds.variant) {
    const updateData = {
      variant_name: `Updated ${testData.variant.variant_name}`,
      price: 149.99
    };
    const updateResult = await makeRequest('PUT', `/variants/${createdIds.variant}`, updateData, true);
    const updatePassed = updateResult.success;
    updateResults(updatePassed);
    log(`  Update Variant: ${updatePassed ? '✅ PASS' : '❌ FAIL'}`, updatePassed ? colors.green : colors.red);
    allPassed = allPassed && updatePassed;
  }

  // DELETE Variant (will be done at the end)

  return allPassed;
};

// ===== PRODUCT PHOTO CRUD TESTS =====
const testPhotoCRUD = async () => {
  log('📸 Testing Product Photo CRUD Operations...', colors.cyan);
  let allPassed = true;

  if (!createdIds.product) {
    log('  ⚠️  Skipping photo tests - no product created', colors.yellow);
    return false;
  }

  // CREATE Photo
  const createResult = await makeRequest('POST', `/products/${createdIds.product}/photos`, testData.photo, true);
  const createPassed = createResult.success;
  updateResults(createPassed);
  log(`  Create Photo: ${createPassed ? '✅ PASS' : '❌ FAIL'}`, createPassed ? colors.green : colors.red);
  if (createPassed) createdIds.photo = createResult.data.id;
  allPassed = allPassed && createPassed;

  // READ Photos (Get by Product ID)
  const getByProductResult = await makeRequest('GET', `/products/${createdIds.product}/photos`);
  const getByProductPassed = getByProductResult.success;
  updateResults(getByProductPassed);
  log(`  Get Photos by Product: ${getByProductPassed ? '✅ PASS' : '❌ FAIL'}`, getByProductPassed ? colors.green : colors.red);
  allPassed = allPassed && getByProductPassed;

  // DELETE Photo (will be done at the end)

  return allPassed;
};

// ===== RATING CRUD TESTS =====
const testRatingCRUD = async () => {
  log('⭐ Testing Rating CRUD Operations...', colors.cyan);
  let allPassed = true;

  if (!createdIds.product) {
    log('  ⚠️  Skipping rating tests - no product created', colors.yellow);
    return false;
  }

  // CREATE Rating
  const createResult = await makeRequest('POST', `/products/${createdIds.product}/ratings`, testData.rating);
  const createPassed = createResult.success;
  updateResults(createPassed);
  log(`  Create Rating: ${createPassed ? '✅ PASS' : '❌ FAIL'}`, createPassed ? colors.green : colors.red);
  if (createPassed) createdIds.rating = createResult.data.id;
  allPassed = allPassed && createPassed;

  // READ Ratings (Get by Product ID)
  const getByProductResult = await makeRequest('GET', `/products/${createdIds.product}/ratings`);
  const getByProductPassed = getByProductResult.success;
  updateResults(getByProductPassed);
  log(`  Get Ratings by Product: ${getByProductPassed ? '✅ PASS' : '❌ FAIL'}`, getByProductPassed ? colors.green : colors.red);
  allPassed = allPassed && getByProductPassed;

  // Note: Ratings typically don't have update/delete operations for data integrity

  return allPassed;
};

// ===== WEB BANNER CRUD TESTS =====
const testBannerCRUD = async () => {
  log('🎯 Testing Web Banner CRUD Operations...', colors.cyan);
  let allPassed = true;

  // CREATE Banner
  const createResult = await makeRequest('POST', '/banners', testData.banner, true);
  const createPassed = createResult.success;
  updateResults(createPassed);
  log(`  Create Banner: ${createPassed ? '✅ PASS' : '❌ FAIL'}`, createPassed ? colors.green : colors.red);
  if (createPassed) createdIds.banner = createResult.data.id;
  allPassed = allPassed && createPassed;

  // READ Banner (Get All)
  const getAllResult = await makeRequest('GET', '/banners');
  const getAllPassed = getAllResult.success;
  updateResults(getAllPassed);
  log(`  Get All Banners: ${getAllPassed ? '✅ PASS' : '❌ FAIL'}`, getAllPassed ? colors.green : colors.red);
  allPassed = allPassed && getAllPassed;

  // READ Banner (Get Active Only)
  const getActiveResult = await makeRequest('GET', '/banners/active');
  const getActivePassed = getActiveResult.success;
  updateResults(getActivePassed);
  log(`  Get Active Banners: ${getActivePassed ? '✅ PASS' : '❌ FAIL'}`, getActivePassed ? colors.green : colors.red);
  allPassed = allPassed && getActivePassed;

  // UPDATE Banner
  if (createdIds.banner) {
    const updateData = {
      title: `Updated ${testData.banner.title}`,
      banner_image_url: 'https://example.com/updated-banner.jpg',
      redirect_url: 'https://example.com/updated-redirect',
      active: false
    };
    const updateResult = await makeRequest('PUT', `/banners/${createdIds.banner}`, updateData, true);
    const updatePassed = updateResult.success;
    updateResults(updatePassed);
    log(`  Update Banner: ${updatePassed ? '✅ PASS' : '❌ FAIL'}`, updatePassed ? colors.green : colors.red);
    allPassed = allPassed && updatePassed;
  }

  // DELETE Banner (will be done at the end)

  return allPassed;
};

// ===== ADMIN CRUD TESTS =====
const testAdminCRUD = async () => {
  log('👤 Testing Admin CRUD Operations...', colors.cyan);
  let allPassed = true;

  // CREATE Admin
  const adminData = {
    username: testData.admin.username,
    password_hash: await bcrypt.hash('testpassword123', 10)
  };

  const createResult = await makeRequest('POST', '/admins', adminData, true);
  const createPassed = createResult.success;
  updateResults(createPassed);
  log(`  Create Admin: ${createPassed ? '✅ PASS' : '❌ FAIL'}`, createPassed ? colors.green : colors.red);
  if (createPassed) createdIds.admin = createResult.data.id;
  allPassed = allPassed && createPassed;

  // READ Admin (Get All)
  const getAllResult = await makeRequest('GET', '/admins', null, true);
  const getAllPassed = getAllResult.success;
  updateResults(getAllPassed);
  log(`  Get All Admins: ${getAllPassed ? '✅ PASS' : '❌ FAIL'}`, getAllPassed ? colors.green : colors.red);
  allPassed = allPassed && getAllPassed;

  // Note: Admin update/delete operations are typically restricted for security

  return allPassed;
};

// ===== DELETE TESTS (Clean up in reverse order to maintain referential integrity) =====
const testDeleteOperations = async () => {
  log('🗑️  Testing Delete Operations...', colors.cyan);
  let allPassed = true;

  // DELETE Photo
  if (createdIds.photo) {
    const deleteResult = await makeRequest('DELETE', `/photos/${createdIds.photo}`, null, true);
    const deletePassed = deleteResult.success;
    updateResults(deletePassed);
    log(`  Delete Photo: ${deletePassed ? '✅ PASS' : '❌ FAIL'}`, deletePassed ? colors.green : colors.red);
    allPassed = allPassed && deletePassed;
  }

  // DELETE Variant
  if (createdIds.variant) {
    const deleteResult = await makeRequest('DELETE', `/variants/${createdIds.variant}`, null, true);
    const deletePassed = deleteResult.success;
    updateResults(deletePassed);
    log(`  Delete Variant: ${deletePassed ? '✅ PASS' : '❌ FAIL'}`, deletePassed ? colors.green : colors.red);
    allPassed = allPassed && deletePassed;
  }

  // DELETE Banner
  if (createdIds.banner) {
    const deleteResult = await makeRequest('DELETE', `/banners/${createdIds.banner}`, null, true);
    const deletePassed = deleteResult.success;
    updateResults(deletePassed);
    log(`  Delete Banner: ${deletePassed ? '✅ PASS' : '❌ FAIL'}`, deletePassed ? colors.green : colors.red);
    allPassed = allPassed && deletePassed;
  }

  // DELETE Product (this will cascade delete ratings, photos, variants)
  if (createdIds.product) {
    const deleteResult = await makeRequest('DELETE', `/products/${createdIds.product}`, null, true);
    const deletePassed = deleteResult.success;
    updateResults(deletePassed);
    log(`  Delete Product: ${deletePassed ? '✅ PASS' : '❌ FAIL'}`, deletePassed ? colors.green : colors.red);
    allPassed = allPassed && deletePassed;
  }

  // DELETE Category
  if (createdIds.category) {
    const deleteResult = await makeRequest('DELETE', `/categories/${createdIds.category}`, null, true);
    const deletePassed = deleteResult.success;
    updateResults(deletePassed);
    log(`  Delete Category: ${deletePassed ? '✅ PASS' : '❌ FAIL'}`, deletePassed ? colors.green : colors.red);
    allPassed = allPassed && deletePassed;
  }

  // DELETE Brand
  if (createdIds.brand) {
    const deleteResult = await makeRequest('DELETE', `/brands/${createdIds.brand}`, null, true);
    const deletePassed = deleteResult.success;
    updateResults(deletePassed);
    log(`  Delete Brand: ${deletePassed ? '✅ PASS' : '❌ FAIL'}`, deletePassed ? colors.green : colors.red);
    allPassed = allPassed && deletePassed;
  }

  return allPassed;
};

// Main test runner - comprehensive CRUD testing
const runAllTests = async () => {
  log('🚀 Comprehensive API CRUD Tests Starting...', colors.cyan);
  log('=' .repeat(50), colors.blue);

  try {
    // System tests
    await testHealthCheck();
    await testDatabaseConnection();

    // Admin authentication
    const loginSuccess = await testAdminLogin();
    if (!loginSuccess) {
      log('❌ Cannot proceed without admin login', colors.red);
      return;
    }

    log('\n📋 Running CRUD Tests for All Tables...', colors.cyan);
    log('-' .repeat(50), colors.blue);

    // CRUD operations for all tables
    await testBrandCRUD();
    await testCategoryCRUD();
    await testProductCRUD();
    await testVariantCRUD();
    await testPhotoCRUD();
    await testRatingCRUD();
    await testBannerCRUD();
    await testAdminCRUD();

    log('\n🗑️  Running Delete Tests...', colors.cyan);
    log('-' .repeat(50), colors.blue);

    // Delete operations (in reverse order for referential integrity)
    await testDeleteOperations();

    // Summary
    log('\n' + '=' .repeat(50), colors.blue);
    log(`📊 Final Results: ${testResults.passed}/${testResults.total} tests passed`,
         testResults.failed === 0 ? colors.green : colors.yellow);

    if (testResults.failed === 0) {
      log('🎉 All comprehensive CRUD tests passed!', colors.green);
      log('✅ Database schema and API endpoints are working correctly', colors.green);
    } else {
      log(`⚠️  ${testResults.failed} tests failed`, colors.yellow);
      log('❌ Some API endpoints or database operations need attention', colors.red);
    }

    // Show created IDs for reference
    log('\n📝 Test Data Created:', colors.blue);
    Object.entries(createdIds).forEach(([key, value]) => {
      if (value) {
        log(`  ${key}: ID ${value}`, colors.cyan);
      }
    });

  } catch (error) {
    log(`💥 Test error: ${error.message}`, colors.red);
    console.error(error);
  }
};



// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests, makeRequest, testData };
