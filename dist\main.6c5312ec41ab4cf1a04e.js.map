{"version": 3, "file": "main.6c5312ec41ab4cf1a04e.js", "mappings": "uBAAIA,E,iECGAC,E,MAA0B,GAA4B,KAE1DA,EAAwBC,KAAK,CAACC,EAAOC,GAAI,whIAoNjB,GAAG,CAAC,QAAU,EAAE,QAAU,CAAC,qCAAqC,MAAQ,GAAG,SAAW,mjEAAmjE,eAAiB,CAAC,i8IAAi8I,WAAa,MAEjoN,S,uIChNIC,EAAU,CAAC,EAEfA,EAAQC,kBAAoB,IAC5BD,EAAQE,cAAgB,IACxBF,EAAQG,OAAS,SAAc,KAAM,QACrCH,EAAQI,OAAS,IACjBJ,EAAQK,mBAAqB,IAEhB,IAAI,IAASL,GAKJ,KAAW,IAAQM,QAAS,IAAQA,O,6oBCxBpDC,EAAU,WAMb,OAAAC,EAAAA,EAAAA,GALD,SAAAD,KAAcE,EAAAA,EAAAA,GAAA,KAAAF,GACZG,KAAKC,QAAU,OACfD,KAAKE,eAAiB,CACpB,eAAgB,mBAEpB,EAAC,EAAAC,IAAA,UAAAC,OAAAC,GAAAC,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAcC,GAAQ,IAAAnB,EAAAoB,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,UAAA,OAAAT,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAMR,OANU7B,EAAOyB,EAAAK,OAAA,QAAAC,IAAAN,EAAA,GAAAA,EAAA,GAAG,CAAC,EAC3BL,EAAM,GAAHY,OAAMtB,KAAKC,SAAOqB,OAAGb,GAExBE,EAAMY,EAAA,CACVC,QAAOD,EAAAA,EAAA,GAAOvB,KAAKE,gBAAmBZ,EAAQkC,SAC9CC,YAAa,WACVnC,GAAO2B,EAAAC,KAAA,EAAAD,EAAAE,KAAA,EAIaO,MAAMhB,EAAKC,GAAO,OAGe,GAHlDC,EAAQK,EAAAU,OAGRd,EAAcD,EAASY,QAAQI,IAAI,mBAGtBf,EAAYgB,SAAS,oBAAmB,CAAAZ,EAAAE,KAAA,eAAAF,EAAAE,KAAA,EAC5CP,EAASkB,OAAM,OAA5BhB,EAAIG,EAAAU,KAAAV,EAAAE,KAAG,EAAH,oBAAAF,EAAAE,KAAG,EAEMP,EAASmB,OAAM,OAA5BjB,EAAIG,EAAAU,KAAA,UAGDf,EAASoB,GAAI,CAAFf,EAAAE,KAAA,WAEM,YAAhBc,EAAAA,EAAAA,GAAOnB,KAAqBA,EAAKoB,QAAO,CAAAjB,EAAAE,KAAA,cACpC,IAAIgB,MAAMrB,EAAKoB,SAAQ,aAEvB,IAAIC,MAAM,QAADb,OAASV,EAASwB,OAAM,MAAAd,OAAKV,EAASyB,aAAa,cAAApB,EAAAqB,OAAA,SAI/DxB,GAAI,OAE6C,MAF7CG,EAAAC,KAAA,EAAAD,EAAA,SAE6C,yBAAAA,EAAAsB,OAAA,EAAA/B,EAAA,iBAG3D,SApCYgC,GAAA,OAAAnC,EAAAoC,MAAC,KAADzB,UAAA,KAAAb,IAAA,MAAAC,OAAAsC,GAAApC,EAAAA,EAAAA,GAAAC,IAAAA,KAsCb,SAAAoC,EAAUlC,GAAQ,IAAAmC,EAAAlC,EAAAmC,EAAA7B,UAAA,OAAAT,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,OAEiD,OAD3DyB,EAAc,IAAIG,gBADAF,EAAAzB,OAAA,QAAAC,IAAAwB,EAAA,GAAAA,EAAA,GAAG,CAAC,GACoBG,WAC1CtC,EAAMkC,EAAc,GAAHtB,OAAMb,EAAQ,KAAAa,OAAIsB,GAAgBnC,EAAQqC,EAAAR,OAAA,SAE1DtC,KAAKiD,QAAQvC,EAAK,CACvBwC,OAAQ,SACR,wBAAAJ,EAAAP,OAAA,EAAAI,EAAA,SACH,SAPQQ,GAAA,OAAAT,EAAAD,MAAC,KAADzB,UAAA,KAAAb,IAAA,OAAAC,OAAAgD,GAAA9C,EAAAA,EAAAA,GAAAC,IAAAA,KAST,SAAA8C,EAAW5C,GAAQ,IAAAK,EAAAwC,EAAAtC,UAAA,OAAAT,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,OAAW,OAATL,EAAIwC,EAAAlC,OAAA,QAAAC,IAAAiC,EAAA,GAAAA,EAAA,GAAG,CAAC,EAACC,EAAAjB,OAAA,SACrBtC,KAAKiD,QAAQxC,EAAU,CAC5ByC,OAAQ,OACRM,KAAMC,KAAKC,UAAU5C,MACrB,wBAAAyC,EAAAhB,OAAA,EAAAc,EAAA,SACH,SALSM,GAAA,OAAAP,EAAAX,MAAC,KAADzB,UAAA,KAAAb,IAAA,MAAAC,OAAAwD,GAAAtD,EAAAA,EAAAA,GAAAC,IAAAA,KAOV,SAAAsD,EAAUpD,GAAQ,IAAAK,EAAAgD,EAAA9C,UAAA,OAAAT,IAAAA,KAAA,SAAAwD,GAAA,cAAAA,EAAA7C,KAAA6C,EAAA5C,MAAA,OAAW,OAATL,EAAIgD,EAAA1C,OAAA,QAAAC,IAAAyC,EAAA,GAAAA,EAAA,GAAG,CAAC,EAACC,EAAAzB,OAAA,SACpBtC,KAAKiD,QAAQxC,EAAU,CAC5ByC,OAAQ,MACRM,KAAMC,KAAKC,UAAU5C,MACrB,wBAAAiD,EAAAxB,OAAA,EAAAsB,EAAA,SACH,SALQG,GAAA,OAAAJ,EAAAnB,MAAC,KAADzB,UAAA,KAAAb,IAAA,SAAAC,OAAA6D,GAAA3D,EAAAA,EAAAA,GAAAC,IAAAA,KAOT,SAAA2D,EAAazD,GAAQ,OAAAF,IAAAA,KAAA,SAAA4D,GAAA,cAAAA,EAAAjD,KAAAiD,EAAAhD,MAAA,cAAAgD,EAAA7B,OAAA,SACZtC,KAAKiD,QAAQxC,EAAU,CAC5ByC,OAAQ,YACR,wBAAAiB,EAAA5B,OAAA,EAAA2B,EAAA,SACH,SAJWE,GAAA,OAAAH,EAAAxB,MAAC,KAADzB,UAAA,KAAAb,IAAA,aAAAC,OAAAiE,GAAA/D,EAAAA,EAAAA,GAAAC,IAAAA,KAMZ,SAAA+D,EAAiB7D,EAAU8D,GAAQ,OAAAhE,IAAAA,KAAA,SAAAiE,GAAA,cAAAA,EAAAtD,KAAAsD,EAAArD,MAAA,cAAAqD,EAAAlC,OAAA,SAC1BtC,KAAKiD,QAAQxC,EAAU,CAC5ByC,OAAQ,OACRM,KAAMe,EACN/C,QAAS,CAAC,KACV,wBAAAgD,EAAAjC,OAAA,EAAA+B,EAAA,SACH,SANeG,EAAAC,GAAA,OAAAL,EAAA5B,MAAC,KAADzB,UAAA,IAQhB,CAAAb,IAAA,SAAAC,OAAAuE,GAAArE,EAAAA,EAAAA,GAAAC,IAAAA,KACA,SAAAqE,EAAaC,GAAQ,OAAAtE,IAAAA,KAAA,SAAAuE,GAAA,cAAAA,EAAA5D,KAAA4D,EAAA3D,MAAA,cAAA2D,EAAAxC,OAAA,SACZtC,KAAK4B,IAAI,IAADN,OAAKuD,KAAW,wBAAAC,EAAAvC,OAAA,EAAAqC,EAAA,SAChC,SAFWG,GAAA,OAAAJ,EAAAlC,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,OAAA4E,GAAA1E,EAAAA,EAAAA,GAAAC,IAAAA,KAIZ,SAAA0E,EAAcJ,EAAUxF,GAAE,OAAAkB,IAAAA,KAAA,SAAA2E,GAAA,cAAAA,EAAAhE,KAAAgE,EAAA/D,MAAA,cAAA+D,EAAA5C,OAAA,SACjBtC,KAAK4B,IAAI,IAADN,OAAKuD,EAAQ,KAAAvD,OAAIjC,KAAK,wBAAA6F,EAAA3C,OAAA,EAAA0C,EAAA,SACtC,SAFYE,EAAAC,GAAA,OAAAJ,EAAAvC,MAAC,KAADzB,UAAA,KAAAb,IAAA,SAAAC,OAAAiF,GAAA/E,EAAAA,EAAAA,GAAAC,IAAAA,KAIb,SAAA+E,EAAaT,EAAU/D,GAAI,OAAAP,IAAAA,KAAA,SAAAgF,GAAA,cAAAA,EAAArE,KAAAqE,EAAApE,MAAA,cAAAoE,EAAAjD,OAAA,SAClBtC,KAAKwF,KAAK,IAADlE,OAAKuD,GAAY/D,IAAK,wBAAAyE,EAAAhD,OAAA,EAAA+C,EAAA,SACvC,SAFWG,EAAAC,GAAA,OAAAL,EAAA5C,MAAC,KAADzB,UAAA,KAAAb,IAAA,SAAAC,OAAAuF,GAAArF,EAAAA,EAAAA,GAAAC,IAAAA,KAIZ,SAAAqF,EAAaf,EAAUxF,EAAIyB,GAAI,OAAAP,IAAAA,KAAA,SAAAsF,GAAA,cAAAA,EAAA3E,KAAA2E,EAAA1E,MAAA,cAAA0E,EAAAvD,OAAA,SACtBtC,KAAK8F,IAAI,IAADxE,OAAKuD,EAAQ,KAAAvD,OAAIjC,GAAMyB,IAAK,wBAAA+E,EAAAtD,OAAA,EAAAqD,EAAA,SAC5C,SAFWG,EAAAC,EAAAC,GAAA,OAAAN,EAAAlD,MAAC,KAADzB,UAAA,KAAAb,IAAA,SAAAC,OAAA8F,GAAA5F,EAAAA,EAAAA,GAAAC,IAAAA,KAIZ,SAAA4F,EAAatB,EAAUxF,GAAE,OAAAkB,IAAAA,KAAA,SAAA6F,GAAA,cAAAA,EAAAlF,KAAAkF,EAAAjF,MAAA,cAAAiF,EAAA9D,OAAA,SAChBtC,KAAI,OAAQ,IAADsB,OAAKuD,EAAQ,KAAAvD,OAAIjC,KAAK,wBAAA+G,EAAA7D,OAAA,EAAA4D,EAAA,SACzC,SAFWE,EAAAC,GAAA,OAAAJ,EAAAzD,MAAC,KAADzB,UAAA,MAJA,IAAAkF,EAJAP,EAJCN,EAJDL,EADZL,EAdYN,EAPHJ,EAPCL,EATDR,EAtCIV,EAFZrC,CA8FW,CApGE,GCEVkG,EAAW,WAUd,OAAAzG,EAAAA,EAAAA,GATD,SAAAyG,KAAcxG,EAAAA,EAAAA,GAAA,KAAAwG,GACZvG,KAAKwG,WAAa,IAAI3G,EACtBG,KAAKyG,iBAAkB,EACvBzG,KAAK0G,KAAO,KACZ1G,KAAK2G,mBAAqB,GAC1B3G,KAAK4G,cAAgB,EACrB5G,KAAK6G,iBAAmB,EACxB7G,KAAK8G,YAAc,IACnB9G,KAAK+G,aAAe,IACtB,EAAC,EAAA5G,IAAA,YAAAC,OAAA4G,GAAA1G,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,IAAA,IAAAI,EAAA,OAAAL,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAC,KAAA,EAAAD,EAAAE,KAAA,EAE2BnB,KAAKwG,WAAW5E,IAAI,kBAAiB,OAA9C,KAARhB,EAAQK,EAAAU,MACDsF,UAAWrG,EAASE,KAAKoG,cAAa,CAAAjG,EAAAE,KAAA,QAGhB,OAFjCnB,KAAKyG,iBAAkB,EACvBzG,KAAK0G,KAAO9F,EAASE,KAAK4F,KAC1B1G,KAAKmH,uBAAsB,GAAMlG,EAAAqB,OAAA,UAC1B,GAAI,OAIuB,OAFlCtC,KAAKyG,iBAAkB,EACvBzG,KAAK0G,KAAO,KACZ1G,KAAKmH,uBAAsB,GAAOlG,EAAAqB,OAAA,UAC3B,GAAK,OAAArB,EAAAE,KAAA,eAMoB,OANpBF,EAAAC,KAAA,EAAAD,EAAA,SAIdjB,KAAKyG,iBAAkB,EACvBzG,KAAK0G,KAAO,KACZ1G,KAAKmH,uBAAsB,GAAOlG,EAAAqB,OAAA,UAC3B,GAAK,wBAAArB,EAAAsB,OAAA,EAAA/B,EAAA,iBAEf,WArBc,OAAAwG,EAAAvE,MAAC,KAADzB,UAAA,KAAAb,IAAA,QAAAC,OAAAgH,GAAA9G,EAAAA,EAAAA,GAAAC,IAAAA,KAuBf,SAAAoC,EAAY0E,EAAUC,GAAQ,IAAAC,EAAA3G,EAAA4G,EAAA,OAAAjH,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,WAExBnB,KAAKyH,kBAAmB,CAAF3E,EAAA3B,KAAA,QACqD,MAAvEoG,EAAgBG,KAAKC,MAAM3H,KAAK+G,aAAea,KAAKC,OAAS,IAAO,IACpE,IAAI1F,MAAM,gCAADb,OAAiCiG,EAAa,cAAY,cAAAzE,EAAA5B,KAAA,EAAA4B,EAAA3B,KAAA,EAIlDnB,KAAKwG,WAAWhB,KAAK,eAAgB,CAC1D6B,SAAAA,EACAC,SAAAA,IACA,OAHY,KAAR1G,EAAQkC,EAAAnB,MAKDsF,QAAS,CAAFnE,EAAA3B,KAAA,QAKe,OAJjCnB,KAAKyG,iBAAkB,EACvBzG,KAAK0G,KAAO9F,EAASE,KAAK4F,KAC1B1G,KAAK4G,cAAgB,EACrB5G,KAAK+G,aAAe,KACpB/G,KAAKmH,uBAAsB,GAAMrE,EAAAR,OAAA,SAC1B1B,GAAQ,OAEU,MAAzBZ,KAAK8H,oBACC,IAAI3F,MAAMvB,EAASsB,SAAW,gBAAe,OAAAY,EAAA3B,KAAA,eAG5B,MAH4B2B,EAAA5B,KAAA,EAAAsG,EAAA1E,EAAA,SAGrD9C,KAAK8H,oBAAoBN,EAAA,wBAAA1E,EAAAP,OAAA,EAAAI,EAAA,iBAG5B,SA5BUH,EAAAW,GAAA,OAAAiE,EAAA3E,MAAC,KAADzB,UAAA,KAAAb,IAAA,SAAAC,OAAA2H,GAAAzH,EAAAA,EAAAA,GAAAC,IAAAA,KA8BX,SAAA8C,IAAA,OAAA9C,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,cAAAoC,EAAArC,KAAA,EAAAqC,EAAApC,KAAA,EAEUnB,KAAKwG,WAAWhB,KAAK,iBAAgB,OAAAjC,EAAApC,KAAA,eAAAoC,EAAArC,KAAA,EAAAqC,EAAA,SAEI,OAIb,OAJaA,EAAArC,KAAA,EAE/ClB,KAAKyG,iBAAkB,EACvBzG,KAAK0G,KAAO,KACZ1G,KAAKmH,uBAAsB,GAAO5D,EAAAyE,OAAA,2BAAAzE,EAAAhB,OAAA,EAAAc,EAAA,qBAErC,WAVW,OAAA0E,EAAAtF,MAAC,KAADzB,UAAA,KAAAb,IAAA,oBAAAC,MAYZ,WACEJ,KAAK4G,gBAED5G,KAAK4G,eAAiB5G,KAAK6G,mBAC7B7G,KAAK+G,aAAea,KAAKC,MAAQ7H,KAAK8G,YACtCmB,aAAaC,QAAQ,eAAgBlI,KAAK+G,aAAa/D,YAE3D,GAAC,CAAA7C,IAAA,kBAAAC,MAED,WACE,IAAM+H,EAAgBF,aAAaG,QAAQ,gBAC3C,GAAID,EAAe,CAEjB,GADAnI,KAAK+G,aAAesB,SAASF,GACzBP,KAAKC,MAAQ7H,KAAK+G,aACpB,OAAO,EAGPkB,aAAaK,WAAW,gBACxBtI,KAAK+G,aAAe,KACpB/G,KAAK4G,cAAgB,CAEzB,CACA,OAAO,CACT,GAAC,CAAAzG,IAAA,uBAAAC,MAED,WACE,OAAOsH,KAAKa,IAAI,EAAGvI,KAAK6G,iBAAmB7G,KAAK4G,cAClD,GAAC,CAAAzG,IAAA,0BAAAC,MAED,WACE,OAAKJ,KAAKyH,kBACHC,KAAKa,IAAI,EAAGvI,KAAK+G,aAAea,KAAKC,OADR,CAEtC,GAAC,CAAA1H,IAAA,oBAAAC,MAED,SAAkBoI,GAAU,IAAAC,EAAA,KAI1B,OAHAzI,KAAK2G,mBAAmBxH,KAAKqJ,GAGtB,WACL,IAAME,EAAQD,EAAK9B,mBAAmBgC,QAAQH,GAC1CE,GAAS,GACXD,EAAK9B,mBAAmBiC,OAAOF,EAAO,EAE1C,CACF,GAAC,CAAAvI,IAAA,wBAAAC,MAED,SAAsBqG,GAAiB,IAAAoC,EAAA,KACrC7I,KAAK2G,mBAAmBmC,QAAQ,SAAAN,GAC9B,IACEA,EAAS/B,EAAiBoC,EAAKnC,KACjC,CAAE,MAAOqC,GAET,CACF,EACF,GAAC,CAAA5I,IAAA,UAAAC,MAED,WACE,OAAOJ,KAAK0G,IACd,GAAC,CAAAvG,IAAA,aAAAC,MAED,WACE,OAAOJ,KAAKyG,eACd,KAxGW,IAAAsB,EAvBIX,EAFdJ,CAiIA,CA3Ic,GCFXgC,EAAmB,WAKtB,OAAAlJ,EAAAA,EAAAA,GAJD,SAAAkJ,KAAcjJ,EAAAA,EAAAA,GAAA,KAAAiJ,GACZhJ,KAAKiJ,UAAY,KACjBjJ,KAAKkJ,cAAgB,GACrBlJ,KAAKmJ,OAAS,CAChB,EAAC,EAAAhJ,IAAA,OAAAC,MAED,WACEJ,KAAKiJ,UAAYG,SAASC,eAAe,qBACpCrJ,KAAKiJ,WAMVjJ,KAAKsJ,WACP,GAAC,CAAAnJ,IAAA,YAAAC,MAED,WACE,IAAIgJ,SAASC,eAAe,uBAA5B,CAEA,IAAME,EAAQH,SAASI,cAAc,SACrCD,EAAMlK,GAAK,sBACXkK,EAAME,YAAc,6wEAuGpBL,SAASM,KAAKC,YAAYJ,EA3GgC,CA4G5D,GAAC,CAAApJ,IAAA,OAAAC,MAED,SAAKwJ,EAAMC,EAAO3H,GAA0B,IAAAuG,EAAA,KAAjBqB,EAAQ9I,UAAAI,OAAA,QAAAC,IAAAL,UAAA,GAAAA,UAAA,GAAG,IAC9B+I,EAAe,CACnB1K,GAAIW,KAAKmJ,SACTS,KAAAA,EACAC,MAAAA,EACA3H,QAAAA,EACA4H,SAAAA,GAaF,OAVA9J,KAAKkJ,cAAc/J,KAAK4K,GACxB/J,KAAKgK,SAGDF,EAAW,GACbG,WAAW,WACTxB,EAAKyB,OAAOH,EAAa1K,GAC3B,EAAGyK,GAGEC,EAAa1K,EACtB,GAAC,CAAAc,IAAA,UAAAC,MAED,SAAQyJ,EAAO3H,EAAS4H,GACtB,OAAO9J,KAAKmK,KAAK,UAAWN,EAAO3H,EAAS4H,EAC9C,GAAC,CAAA3J,IAAA,QAAAC,MAED,SAAMyJ,EAAO3H,EAAS4H,GACpB,OAAO9J,KAAKmK,KAAK,QAASN,EAAO3H,EAAS4H,EAC5C,GAAC,CAAA3J,IAAA,UAAAC,MAED,SAAQyJ,EAAO3H,EAAS4H,GACtB,OAAO9J,KAAKmK,KAAK,UAAWN,EAAO3H,EAAS4H,EAC9C,GAAC,CAAA3J,IAAA,OAAAC,MAED,SAAKyJ,EAAO3H,EAAS4H,GACnB,OAAO9J,KAAKmK,KAAK,OAAQN,EAAO3H,EAAS4H,EAC3C,GAAC,CAAA3J,IAAA,SAAAC,MAED,SAAOf,GACL,IAAMqJ,EAAQ1I,KAAKkJ,cAAckB,UAAU,SAAAC,GAAC,OAAIA,EAAEhL,KAAOA,CAAE,GACvDqJ,GAAS,IACX1I,KAAKkJ,cAAcN,OAAOF,EAAO,GACjC1I,KAAKgK,SAET,GAAC,CAAA7J,IAAA,QAAAC,MAED,WACEJ,KAAKkJ,cAAgB,GACrBlJ,KAAKgK,QACP,GAAC,CAAA7J,IAAA,SAAAC,MAED,WAAS,IAAAyI,EAAA,KACP,GAAK7I,KAAKiJ,UAAV,CAGA,IAAIqB,EAAwBtK,KAAKiJ,UAAUsB,cAAc,2BACpDD,KACHA,EAAwBlB,SAASI,cAAc,QACzBgB,UAAY,yBAClCxK,KAAKiJ,UAAUU,YAAYW,IAI7BA,EAAsBG,UAAY,GAGlCzK,KAAKkJ,cAAcJ,QAAQ,SAAAiB,GACzB,IAAMW,EAAU7B,EAAK8B,0BAA0BZ,GAC/CO,EAAsBX,YAAYe,GAGlCT,WAAW,WACTS,EAAQE,UAAUC,IAAI,OACxB,EAAG,GACL,EAtB2B,CAuB7B,GAAC,CAAA1K,IAAA,4BAAAC,MAED,SAA0B2J,GAAc,IAAAe,EAAA,KAChCJ,EAAUtB,SAASI,cAAc,OACvCkB,EAAQF,UAAY,gBAAHlJ,OAAmByI,EAAaH,MACjDc,EAAQK,QAAQ1L,GAAK0K,EAAa1K,GAElC,IAAM2L,EAAOhL,KAAKiL,QAAQlB,EAAaH,MAmBvC,OAjBAc,EAAQD,UAAY,0CAAHnJ,OACkB0J,EAAI,8FAAA1J,OAEDtB,KAAKkL,WAAWnB,EAAaF,OAAM,sDAAAvI,OACjCtB,KAAKkL,WAAWnB,EAAa7H,SAAQ,8IAQ5DwI,EAAQH,cAAc,uBAC9BY,iBAAiB,QAAS,WACjCL,EAAKZ,OAAOH,EAAa1K,GAC3B,GAEOqL,CACT,GAAC,CAAAvK,IAAA,UAAAC,MAED,SAAQwJ,GACN,IAAMwB,EAAQ,CACZnE,QAAS,8DACT8B,MAAO,oEACPsC,QAAS,sEACTC,KAAM,8DAER,OAAOF,EAAMxB,IAASwB,EAAME,IAC9B,GAAC,CAAAnL,IAAA,aAAAC,MAED,SAAW2B,GACT,IAAMwJ,EAAMnC,SAASI,cAAc,OAEnC,OADA+B,EAAI9B,YAAc1H,EACXwJ,EAAId,SACb,IAAC,CAvPsB,GCqTzB,QAlTe,WAMZ,OAAA3K,EAAAA,EAAAA,GALD,SAAA0L,KAAczL,EAAAA,EAAAA,GAAA,KAAAyL,GACZxL,KAAKyL,YAAc,IAAIlF,EACvBvG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAK2L,cAAe,EACpB3L,KAAK4L,WAAY,CACnB,EAAC,EAAAzL,IAAA,SAAAC,OAAAyL,GAAAvL,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAayI,GAAS,OAAA1I,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OACpB8H,EAAUwB,UAAYzK,KAAK8L,UAC3B9L,KAAK+L,aACL/L,KAAKgM,sBAAsB,wBAAA/K,EAAAsB,OAAA,EAAA/B,EAAA,SAC5B,SAJWgC,GAAA,OAAAqJ,EAAApJ,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,MAMZ,WACE,MAAO,8kKA0LT,GAAC,CAAAD,IAAA,aAAAC,MAED,WAAa,IAAAqI,EAAA,KACLwD,EAAO7C,SAASC,eAAe,aAC/B6C,EAAiB9C,SAASC,eAAe,kBACzC8C,EAAgB/C,SAASC,eAAe,YACxC+C,EAAqBhD,SAASC,eAAe,sBAGnD4C,EAAKd,iBAAiB,SAAU,SAACkB,GAC/BA,EAAEC,iBACF7D,EAAK8D,aACP,GAGAL,EAAef,iBAAiB,QAAS,WACvC1C,EAAKkD,cAAgBlD,EAAKkD,aAC1BQ,EAAcvC,KAAOnB,EAAKkD,aAAe,OAAS,WAClDS,EAAmB5B,UAAY/B,EAAKkD,aAAe,mBAAqB,YAC1E,GAGAQ,EAAchB,iBAAiB,WAAY,SAACkB,GAC5B,UAAVA,EAAElM,KACJsI,EAAK8D,aAET,EACF,GAAC,CAAApM,IAAA,cAAAC,OAAAoM,GAAAlM,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAoC,IAAA,IAAA8J,EAAAC,EAAAC,EAAAC,EAAAT,EAAA9E,EAAAC,EAAAuF,EAAA,OAAAtM,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,WACMnB,KAAK4L,UAAW,CAAF9I,EAAA3B,KAAA,eAAA2B,EAAAR,OAAA,iBAUkB,GARvB8G,SAASC,eAAe,aAC/BoD,EAAYrD,SAASC,eAAe,eACpCqD,EAAaD,EAAUlC,cAAc,gBACrCoC,EAAgBF,EAAUlC,cAAc,mBACxCqC,EAAgBxD,SAASC,eAAe,YACxC8C,EAAgB/C,SAASC,eAAe,YAExChC,EAAWuF,EAAcxM,MAAM0M,OAC/BxF,EAAW6E,EAAc/L,MAE1BiH,GAAaC,EAAQ,CAAAxE,EAAA3B,KAAA,QACsE,OAA9FnB,KAAK0L,oBAAoB3C,MAAM,mBAAoB,2CAA2CjG,EAAAR,OAAA,iBAOvD,OAHzCtC,KAAK4L,WAAY,EACjBa,EAAUM,UAAW,EACrBL,EAAW9B,UAAUC,IAAI,UACzB8B,EAAc/B,UAAUV,OAAO,UAAUpH,EAAA5B,KAAA,EAAA4B,EAAA3B,KAAA,EAGjCnB,KAAKyL,YAAYuB,MAAM3F,EAAUC,GAAS,OAChDtH,KAAK0L,oBAAoBzE,QAAQ,mBAAoB,kCAGrDgD,WAAW,WACTgD,OAAOC,SAASC,KAAO,kBACzB,EAAG,KAAMrK,EAAA3B,KAAA,eAAA2B,EAAA5B,KAAA,EAAA2L,EAAA/J,EAAA,SAIT9C,KAAK0L,oBAAoB3C,MAAM,eAAgB8D,EAAM3K,SACrDlC,KAAKgM,sBAGLG,EAAc/L,MAAQ,GACtB+L,EAAciB,QAAQ,OAMgB,OANhBtK,EAAA5B,KAAA,EAGtBlB,KAAK4L,WAAY,EACjBa,EAAUM,UAAW,EACrBL,EAAW9B,UAAUV,OAAO,UAC5ByC,EAAc/B,UAAUC,IAAI,UAAU/H,EAAAkF,OAAA,2BAAAlF,EAAAP,OAAA,EAAAI,EAAA,qBAEzC,WA/CgB,OAAA6J,EAAA/J,MAAC,KAADzB,UAAA,KAAAb,IAAA,sBAAAC,MAiDjB,WACE,IAAMiN,EAAoBjE,SAASC,eAAe,iBAClD,GAAKgE,EAAL,CAEA,IAAMC,EAAoBtN,KAAKyL,YAAY8B,uBACrCC,EAAWxN,KAAKyL,YAAYhE,kBAC5BX,EAAc9G,KAAKyL,YAAYgC,0BAErC,GAAID,EAAU,CACZ,IAAME,EAAUhG,KAAKC,KAAKb,EAAc,IAAO,IAC/CuG,EAAkB5C,UAAY,+EAAHnJ,OAEMoM,EAAO,WAAApM,OAAsB,IAAZoM,EAAgB,IAAM,GAAE,aAE1EL,EAAkB7C,UAAY,sBAChC,MAAW8C,EAAoB,GAAKA,EAAoB,GACtDD,EAAkB5C,UAAY,kEAAHnJ,OAEvBgM,EAAiB,kBAAAhM,OAAuC,IAAtBgM,EAA0B,IAAM,GAAE,sBAExED,EAAkB7C,UAAY,0BAE9B6C,EAAkB7C,UAAY,uBApBF,CAsBhC,KA3EC,IAAAgC,EA9NAX,CAySA,CA/SY,GCwdf,QAxdoB,WAKjB,OAAA/L,EAAAA,EAAAA,GAJD,SAAA6N,KAAc5N,EAAAA,EAAAA,GAAA,KAAA4N,GACZ3N,KAAKyL,YAAc,IAAIlF,EACvBvG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAK4N,kBAAmB,CAC1B,EAAC,EAAAzN,IAAA,SAAAC,OAAAyL,GAAAvL,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAayI,GAAS,OAAA1I,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAEF,OADlB8H,EAAUwB,UAAYzK,KAAK8L,UAC3B9L,KAAK+L,aAAa9K,EAAAE,KAAA,EACZnB,KAAK6N,eAAc,wBAAA5M,EAAAsB,OAAA,EAAA/B,EAAA,SAC1B,SAJWgC,GAAA,OAAAqJ,EAAApJ,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,MAMZ,WACE,MAAO,gtSA+UT,GAAC,CAAAD,IAAA,aAAAC,MAED,WAAa,IAAAqI,EAAA,KACLqF,EAAgB1E,SAASC,eAAe,iBACxC0E,EAAmB3E,SAASC,eAAe,oBAC3C2E,EAAiB5E,SAASC,eAAe,kBACzC4E,EAAmB7E,SAASC,eAAe,oBAC3C6E,EAAY9E,SAASC,eAAe,aACpC8E,EAAU/E,SAASC,eAAe,gBAGxCyE,SAAAA,EAAe3C,iBAAiB,QAAS,WACvC1C,EAAKmF,kBAAoBnF,EAAKmF,iBAC9BO,EAAQvD,UAAUwD,OAAO,YAAa3F,EAAKmF,iBAC7C,GAGAG,SAAAA,EAAkB5C,iBAAiB,QAAS,WAC1CgD,EAAQvD,UAAUwD,OAAO,cAC3B,GAGAJ,SAAAA,EAAgB7C,iBAAiB,QAAS,SAACkB,GACzCA,EAAEgC,kBACFJ,EAAiBrD,UAAUwD,OAAO,OACpC,GAGAhF,SAAS+B,iBAAiB,QAAS,WACjC8C,EAAiBrD,UAAUV,OAAO,OACpC,GAGAgE,SAAAA,EAAW/C,iBAAiB,QAAO,eAAAmD,GAAAhO,EAAAA,EAAAA,GAAAC,IAAAA,KAAE,SAAAoC,EAAO0J,GAAC,OAAA9L,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,OACxB,OAAnBkL,EAAEC,iBAAiBxJ,EAAA3B,KAAA,EACbsH,EAAK8F,eAAc,wBAAAzL,EAAAP,OAAA,EAAAI,EAAA,IAC1B,gBAAAQ,GAAA,OAAAmL,EAAA7L,MAAA,KAAAzB,UAAA,EAHkC,IAMlBoI,SAASoF,iBAAiB,aAClC1F,QAAQ,SAAA2F,GACfA,EAAKtD,iBAAiB,QAAS,SAACkB,GAC9BA,EAAEC,iBACF,IAAMoC,EAAQD,EAAK1D,QAAQ2D,MACvBA,GACFjG,EAAKkG,gBAAgBD,EAEzB,EACF,GAGA1O,KAAK4O,qBACP,GAAC,CAAAzO,IAAA,eAAAC,OAAAyO,GAAAvO,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAA8C,IAAA,IAAAqD,EAAAoI,EAAA,OAAAvO,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,OACE,KACQuF,EAAO1G,KAAKyL,YAAYsD,aAEtBD,EAAkB1F,SAASC,eAAe,eAE9CyF,EAAgBrF,YAAc/C,EAAKW,UAAY,QAGrD,CAAE,MAAO0B,GAET,CAAC,wBAAAxF,EAAAhB,OAAA,EAAAc,EAAA,SACF,WAZiB,OAAAwL,EAAApM,MAAC,KAADzB,UAAA,KAAAb,IAAA,eAAAC,OAAA4O,GAAA1O,EAAAA,EAAAA,GAAAC,IAAAA,KAclB,SAAAsD,IAAA,OAAAtD,IAAAA,KAAA,SAAAwD,GAAA,cAAAA,EAAA7C,KAAA6C,EAAA5C,MAAA,cAAA4C,EAAA7C,KAAA,EAAA6C,EAAA5C,KAAA,EAEUnB,KAAKyL,YAAYwD,SAAQ,OAC/BjP,KAAK0L,oBAAoBzE,QAAQ,aAAc,yCAC/CgG,OAAOC,SAASC,KAAO,SAASpJ,EAAA5C,KAAA,eAAA4C,EAAA7C,KAAA,EAAA6C,EAAA,SAGhC/D,KAAK0L,oBAAoB3C,MAAM,eAAgB,kCAAkC,wBAAAhF,EAAAxB,OAAA,EAAAsB,EAAA,iBAEpF,WATiB,OAAAmL,EAAAvM,MAAC,KAADzB,UAAA,KAAAb,IAAA,kBAAAC,MAWlB,SAAgBsO,GAEGtF,SAASoF,iBAAiB,aAClC1F,QAAQ,SAAA2F,GACfA,EAAK7D,UAAUV,OAAO,UAClBuE,EAAK1D,QAAQ2D,QAAUA,GACzBD,EAAK7D,UAAUC,IAAI,SAEvB,GAGA,IAAMqE,EAAY9F,SAASC,eAAe,aAC1C,GAAI6F,EAAW,CASbA,EAAUzF,YARK,CACb,mBAAoB,YACpB,kBAAmB,WACnB,gBAAiB,SACjB,oBAAqB,aACrB,iBAAkB,UAClB,gBAAiB,eAEYiF,IAAU,WAC3C,CAGIzB,OAAOkC,UACTlC,OAAOkC,QAAQC,UAAU,CAAC,EAAG,GAAIV,GACjCzB,OAAOoC,cAAc,IAAIC,cAAc,aAE3C,GAAC,CAAAnP,IAAA,sBAAAC,MAED,WACE,IAAMmP,EAActC,OAAOC,SAASsC,SACnBpG,SAASoF,iBAAiB,aAElC1F,QAAQ,SAAA2F,GACfA,EAAK7D,UAAUV,OAAO,UAClBuE,EAAK1D,QAAQ2D,QAAUa,GACzBd,EAAK7D,UAAUC,IAAI,SAEvB,EACF,KAlEkB,IAAAmE,EAFjBH,EA5YAhD,CAgdA,CArdiB,GCgYpB,QAhYmB,WAUhB,OAAA/L,EAAAA,EAAAA,GATD,SAAA2P,KAAc1P,EAAAA,EAAAA,GAAA,KAAA0P,GACZzP,KAAKwG,WAAa,IAAI3G,EACtBG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAK0P,MAAQ,CACXC,SAAU,EACVC,OAAQ,EACRC,WAAY,EACZC,QAAS,EAEb,EAAC,EAAA3P,IAAA,SAAAC,OAAAyL,GAAAvL,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAayI,GAAS,OAAA1I,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OACiB,OAArC8H,EAAUwB,UAAYzK,KAAK8L,UAAU7K,EAAAE,KAAA,EAC/BnB,KAAK+P,YAAW,wBAAA9O,EAAAsB,OAAA,EAAA/B,EAAA,SACvB,SAHWgC,GAAA,OAAAqJ,EAAApJ,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,MAKZ,WACE,MAAO,6uRAgTT,GAAC,CAAAD,IAAA,YAAAC,OAAA4P,GAAA1P,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAoC,IAAA,IAAAsN,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAX,EAAAC,EAAAC,EAAAC,EAAA,OAAAvP,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,cAAA2B,EAAA5B,KAAA,EAAA4B,EAAA3B,KAAA,EAG0DoP,QAAQC,IAAI,CAChExQ,KAAKwG,WAAW5E,IAAI,aAAY,MAAO,iBAAO,CAAEd,KAAM,GAAI,GAC1Dd,KAAKwG,WAAW5E,IAAI,WAAU,MAAO,iBAAO,CAAEd,KAAM,GAAI,GACxDd,KAAKwG,WAAW5E,IAAI,eAAc,MAAO,iBAAO,CAAEd,KAAM,GAAI,GAC5Dd,KAAKwG,WAAW5E,IAAI,mBAAkB,MAAO,iBAAO,CAAEd,KAAM,GAAI,KAChE,OAWF,OAXEuP,EAAAvN,EAAAnB,KAAA2O,GAAAG,EAAAA,EAAAA,GAAAJ,EAAA,GALKV,EAAQW,EAAA,GAAEV,EAAMU,EAAA,GAAET,EAAUS,EAAA,GAAER,EAAOQ,EAAA,GAQ5CtQ,KAAK0P,MAAMC,UAAwB,QAAbM,EAAAN,EAAS7O,YAAI,IAAAmP,OAAA,EAAbA,EAAe7O,SAAU,EAC/CpB,KAAK0P,MAAME,QAAoB,QAAXM,EAAAN,EAAO9O,YAAI,IAAAoP,OAAA,EAAXA,EAAa9O,SAAU,EAC3CpB,KAAK0P,MAAMG,YAA4B,QAAfM,EAAAN,EAAW/O,YAAI,IAAAqP,OAAA,EAAfA,EAAiB/O,SAAU,EACnDpB,KAAK0P,MAAMI,SAAsB,QAAZM,EAAAN,EAAQhP,YAAI,IAAAsP,OAAA,EAAZA,EAAchP,SAAU,EAG7CpB,KAAK0Q,qBAEL5N,EAAA3B,KAAA,EACMnB,KAAK2Q,yBAAwB,OAAA7N,EAAA3B,KAAA,eAAA2B,EAAA5B,KAAA,EAAA4B,EAAA,SAInC9C,KAAK0L,oBAAoB3C,MAAM,QAAS,uCAAuC,wBAAAjG,EAAAP,OAAA,EAAAI,EAAA,iBAElF,WA1Bc,OAAAqN,EAAAvN,MAAC,KAADzB,UAAA,KAAAb,IAAA,qBAAAC,MA4Bf,WACE,IAAMwQ,EAAW,CACfC,cAAezH,SAASC,eAAe,iBACvCyH,YAAa1H,SAASC,eAAe,eACrC0H,gBAAiB3H,SAASC,eAAe,mBACzC2H,aAAc5H,SAASC,eAAe,iBAGpCuH,EAASC,gBAAeD,EAASC,cAAcpH,YAAczJ,KAAK0P,MAAMC,UACxEiB,EAASE,cAAaF,EAASE,YAAYrH,YAAczJ,KAAK0P,MAAME,QACpEgB,EAASG,kBAAiBH,EAASG,gBAAgBtH,YAAczJ,KAAK0P,MAAMG,YAC5Ee,EAASI,eAAcJ,EAASI,aAAavH,YAAczJ,KAAK0P,MAAMI,QAC5E,GAAC,CAAA3P,IAAA,yBAAAC,OAAA6Q,GAAA3Q,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAA8C,IAAA,IAAA6N,EAAAC,EAAA,OAAA5Q,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,cAAAoC,EAAArC,KAAA,EAAAqC,EAAApC,KAAA,EAEUnB,KAAKwG,WAAW5E,IAAI,YAAW,QAC/BsP,EAAW9H,SAASC,eAAe,eAEvC6H,EAAS1G,UAAY,0BACrB0G,EAASzG,UAAY,wCACtBlH,EAAApC,KAAA,eAAAoC,EAAArC,KAAA,EAAAqC,EAAA,UAEK2N,EAAW9H,SAASC,eAAe,eAEvC6H,EAAS1G,UAAY,2BACrB0G,EAASzG,UAAY,yCACtB,wBAAAlH,EAAAhB,OAAA,EAAAc,EAAA,iBAEJ,WAf2B,OAAA4N,EAAAxO,MAAC,KAADzB,UAAA,MAF3B,IAAAiQ,EA1CAjB,EAxTAnE,CAoW2B,CA9WX,GCyhBnB,QAzhBkB,WASf,OAAA/L,EAAAA,EAAAA,GARD,SAAAsR,KAAcrR,EAAAA,EAAAA,GAAA,KAAAqR,GACZpR,KAAKwG,WAAa,IAAI3G,EACtBG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAK2P,SAAW,GAChB3P,KAAK4P,OAAS,GACd5P,KAAK6P,WAAa,GAClB7P,KAAKqR,UAAW,EAChBrR,KAAKsR,eAAiB,IACxB,EAAC,EAAAnR,IAAA,SAAAC,OAAAyL,GAAAvL,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAayI,GAAS,OAAA1I,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAEF,OADlB8H,EAAUwB,UAAYzK,KAAK8L,UAC3B9L,KAAK+L,aAAa9K,EAAAE,KAAA,EACZnB,KAAKuR,WAAU,wBAAAtQ,EAAAsB,OAAA,EAAA/B,EAAA,SACtB,SAJWgC,GAAA,OAAAqJ,EAAApJ,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,MAMZ,WACE,MAAO,uoPA8RT,GAAC,CAAAD,IAAA,aAAAC,MAED,WAAa,IAAAqI,EAAA,KACL+I,EAAgBpI,SAASC,eAAe,iBACxCoI,EAAerI,SAASC,eAAe,gBACvCqI,EAAgBtI,SAASC,eAAe,iBACxCsI,EAAcvI,SAASC,eAAe,eACtCuI,EAAQxI,SAASC,eAAe,oBAChCwI,EAAgBD,aAAK,EAALA,EAAOrH,cAAc,mBAE3CiH,SAAAA,EAAerG,iBAAiB,QAAS,kBAAM1C,EAAKqJ,iBAAiB,GACrEL,SAAAA,EAActG,iBAAiB,QAAS,kBAAM1C,EAAKsJ,iBAAiB,GACpEL,SAAAA,EAAevG,iBAAiB,QAAS,kBAAM1C,EAAKsJ,iBAAiB,GACrEF,SAAAA,EAAe1G,iBAAiB,QAAS,kBAAM1C,EAAKsJ,iBAAiB,GACrEJ,SAAAA,EAAaxG,iBAAiB,SAAU,SAACkB,GAAC,OAAK5D,EAAKuJ,iBAAiB3F,EAAE,EACzE,GAAC,CAAAlM,IAAA,WAAAC,OAAA6R,GAAA3R,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAoC,IAAA,IAAA0N,EAAAC,EAAA4B,EAAAC,EAAAC,EAAA,OAAA7R,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,cAAA2B,EAAA5B,KAAA,EAAA4B,EAAA3B,KAAA,EAEyEoP,QAAQC,IAAI,CAC/ExQ,KAAKwG,WAAW5E,IAAI,aACpB5B,KAAKwG,WAAW5E,IAAI,WACpB5B,KAAKwG,WAAW5E,IAAI,iBACpB,OAAAyO,EAAAvN,EAAAnB,KAAA2O,GAAAG,EAAAA,EAAAA,GAAAJ,EAAA,GAJK6B,EAAgB5B,EAAA,GAAE6B,EAAc7B,EAAA,GAAE8B,EAAkB9B,EAAA,GAM3DtQ,KAAK2P,SAAWuC,EAAiBpR,MAAQ,GACzCd,KAAK4P,OAASuC,EAAerR,MAAQ,GACrCd,KAAK6P,WAAauC,EAAmBtR,MAAQ,GAE7Cd,KAAKqS,qBACLrS,KAAKsS,sBAAsBxP,EAAA3B,KAAA,eAAA2B,EAAA5B,KAAA,EAAA4B,EAAA,SAI3B9C,KAAK0L,oBAAoB3C,MAAM,QAAS,gCACxC/I,KAAKuS,cAAc,wBAAAzP,EAAAP,OAAA,EAAAI,EAAA,iBAEtB,WApBa,OAAAsP,EAAAxP,MAAC,KAADzB,UAAA,KAAAb,IAAA,qBAAAC,MAsBd,WAAqB,IAAAyI,EAAA,KACbI,EAAYG,SAASC,eAAe,gBAC1C,GAAKJ,EAEL,GAA6B,IAAzBjJ,KAAK2P,SAASvO,OAAlB,CAWA,IAAMoR,EAAY,4RAAHlR,OAYPtB,KAAK2P,SAAS8C,IAAI,SAAAC,GAAO,uHAAApR,OAIYuH,EAAKqC,WAAWwH,EAAQC,MAAK,8BAAArR,OAC1DoR,EAAQE,YAAc,sCAAHtR,OAAyCuH,EAAKqC,WAAWwH,EAAQE,YAAYC,UAAU,EAAG,MAAIvR,OAAGoR,EAAQE,YAAYxR,OAAS,GAAK,MAAQ,GAAE,UAAW,GAAE,qEAAAE,OAG7KoR,EAAQI,YAAc,IAAG,6BAAAxR,OACzBoR,EAAQK,eAAiB,IAAG,6BAAAzR,OAC5B,IAAIsG,KAAK8K,EAAQM,YAAYC,qBAAoB,2KAAA3R,OAG0BoR,EAAQrT,GAAE,qMAAAiC,OAGNoR,EAAQrT,GAAE,mLAMhG6T,KAAK,IAAG,4CAKjBjK,EAAUwB,UAAY+H,EAGtBvF,OAAOkG,aAAenT,IA5CtB,MAREiJ,EAAUwB,UAAY,8MAqD1B,GAAC,CAAAtK,IAAA,cAAAC,MAED,WACE,IAAM6I,EAAYG,SAASC,eAAe,gBACtCJ,IACFA,EAAUwB,UAAY,0UAS1B,GAAC,CAAAtK,IAAA,sBAAAC,MAED,WAAsB,IAAA0K,EAAA,KACdsI,EAAchK,SAASC,eAAe,gBACtCgK,EAAiBjK,SAASC,eAAe,mBAE3C+J,IACFA,EAAY3I,UAAY,yCACtBzK,KAAK4P,OAAO6C,IAAI,SAAAa,GAAK,wBAAAhS,OAAsBgS,EAAMjU,GAAE,MAAAiC,OAAKwJ,EAAKI,WAAWoI,EAAMX,MAAK,eAAaO,KAAK,KAGrGG,IACFA,EAAe5I,UAAY,4CACzBzK,KAAK6P,WAAW4C,IAAI,SAAAc,GAAQ,wBAAAjS,OAAsBiS,EAASlU,GAAE,MAAAiC,OAAKwJ,EAAKI,WAAWqI,EAASZ,MAAK,eAAaO,KAAK,IAExH,GAAC,CAAA/S,IAAA,kBAAAC,MAED,WAAgC,IAAhBsS,EAAO1R,UAAAI,OAAA,QAAAC,IAAAL,UAAA,GAAAA,UAAA,GAAG,KACxBhB,KAAKsR,eAAiBoB,EACtB,IAAMd,EAAQxI,SAASC,eAAe,oBAChC4C,EAAO7C,SAASC,eAAe,eAC/BQ,EAAQT,SAASC,eAAe,aAElCqJ,GACF7I,EAAMJ,YAAc,eACpBzJ,KAAKwT,aAAad,KAElB7I,EAAMJ,YAAc,kBACpBwC,EAAKwH,SAGP7B,EAAMrI,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,QACjC,GAAC,CAAAxT,IAAA,kBAAAC,MAED,WACgBgJ,SAASC,eAAe,oBAChCE,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,GAC/B3T,KAAKsR,eAAiB,IACxB,GAAC,CAAAnR,IAAA,eAAAC,MAED,SAAasS,GACXtJ,SAASC,eAAe,eAAejJ,MAAQsS,EAAQC,MAAQ,GAC/DvJ,SAASC,eAAe,sBAAsBjJ,MAAQsS,EAAQE,aAAe,GAC7ExJ,SAASC,eAAe,gBAAgBjJ,MAAQsS,EAAQkB,UAAY,GACpExK,SAASC,eAAe,mBAAmBjJ,MAAQsS,EAAQmB,aAAe,EAC5E,GAAC,CAAA1T,IAAA,mBAAAC,OAAA0T,GAAAxT,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAA8C,EAAuBgJ,GAAC,IAAA0H,EAAAC,EAAAC,EAAA1P,EAAA2P,EAAA1M,EAAA,OAAAjH,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,OAiBgB,GAhBtCkL,EAAEC,iBAEIyH,EAAU3K,SAASC,eAAe,kBAClC2K,EAAUD,EAAQxJ,cAAc,aAChC0J,EAAaF,EAAQxJ,cAAc,gBAEnChG,EAAW,IAAI4P,SAAS9H,EAAE+H,QAC1BF,EAAc,CAClBvB,KAAMpO,EAAS3C,IAAI,QACnBgR,YAAarO,EAAS3C,IAAI,eAC1BgS,SAAUrP,EAAS3C,IAAI,aAAe,KACtCiS,YAAatP,EAAS3C,IAAI,gBAAkB,MAG9CmS,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUC,IAAI,UACtBoJ,EAAWrJ,UAAUV,OAAO,UAAU3G,EAAArC,KAAA,GAGhClB,KAAKsR,eAAgB,CAAF/N,EAAApC,KAAA,eAAAoC,EAAApC,KAAA,EACfnB,KAAKwG,WAAWV,IAAI,aAADxE,OAActB,KAAKsR,eAAejS,IAAM6U,GAAY,OAC7ElU,KAAK0L,oBAAoBzE,QAAQ,UAAW,gCAAgC1D,EAAApC,KAAA,sBAAAoC,EAAApC,KAAA,EAEtEnB,KAAKwG,WAAWhB,KAAK,YAAa0O,GAAY,OACpDlU,KAAK0L,oBAAoBzE,QAAQ,UAAW,gCAAgC,OAGvD,OAAvBjH,KAAK+R,kBAAkBxO,EAAApC,KAAA,EACjBnB,KAAKuR,WAAU,OAAAhO,EAAApC,KAAA,eAAAoC,EAAArC,KAAA,EAAAsG,EAAAjE,EAAA,SAIrBvD,KAAK0L,oBAAoB3C,MAAM,QAASvB,EAAMtF,SAAW,0BAA0B,OAIhD,OAJgDqB,EAAArC,KAAA,EAEnF6S,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUV,OAAO,UACzB+J,EAAWrJ,UAAUC,IAAI,UAAUtH,EAAAyE,OAAA,2BAAAzE,EAAAhB,OAAA,EAAAc,EAAA,qBAEtC,SAvCqBF,GAAA,OAAA2Q,EAAArR,MAAC,KAADzB,UAAA,KAAAb,IAAA,cAAAC,MAyCtB,SAAYf,GACV,IAAMqT,EAAU1S,KAAK2P,SAAS0E,KAAK,SAAAC,GAAC,OAAIA,EAAEjV,KAAOA,CAAE,GAC/CqT,GACF1S,KAAK8R,gBAAgBY,EAEzB,GAAC,CAAAvS,IAAA,gBAAAC,OAAAmU,GAAAjU,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAsD,EAAoBxE,GAAE,IAAAqT,EAAA8B,EAAA,OAAAjU,IAAAA,KAAA,SAAAwD,GAAA,cAAAA,EAAA7C,KAAA6C,EAAA5C,MAAA,OACgC,GAA9CuR,EAAU1S,KAAK2P,SAAS0E,KAAK,SAAAC,GAAC,OAAIA,EAAEjV,KAAOA,CAAE,GACrC,CAAF0E,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,oBAEPmS,QAAQ,oCAADnT,OAAqCoR,EAAQC,KAAI,qCAAqC,CAAF5O,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,wBAAAyB,EAAA7C,KAAA,EAAA6C,EAAA5C,KAAA,EAKxFnB,KAAKwG,WAAU,OAAQ,aAADlF,OAAcjC,IAAK,OAC6B,OAA5EW,KAAK0L,oBAAoBzE,QAAQ,UAAW,gCAAgClD,EAAA5C,KAAA,EACtEnB,KAAKuR,WAAU,OAAAxN,EAAA5C,KAAA,eAAA4C,EAAA7C,KAAA,EAAAsT,EAAAzQ,EAAA,SAGrB/D,KAAK0L,oBAAoB3C,MAAM,QAASyL,EAAMtS,SAAW,4BAA4B,wBAAA6B,EAAAxB,OAAA,EAAAsB,EAAA,iBAExF,SAhBkBF,GAAA,OAAA4Q,EAAA9R,MAAC,KAADzB,UAAA,KAAAb,IAAA,aAAAC,MAkBnB,SAAW2B,GACT,IAAMwJ,EAAMnC,SAASI,cAAc,OAEnC,OADA+B,EAAI9B,YAAc1H,EACXwJ,EAAId,SACb,KAxBC,IAAA8J,EAhDAT,EA/IA7B,EAtTApG,CA6gBA,CAthBe,GC0flB,QA1fgB,WAOb,OAAA/L,EAAAA,EAAAA,GAND,SAAA4U,KAAc3U,EAAAA,EAAAA,GAAA,KAAA2U,GACZ1U,KAAKwG,WAAa,IAAI3G,EACtBG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAK4P,OAAS,GACd5P,KAAKqR,UAAW,EAChBrR,KAAK2U,aAAe,IACtB,EAAC,EAAAxU,IAAA,SAAAC,OAAAyL,GAAAvL,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAayI,GAAS,OAAA1I,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAEF,OADlB8H,EAAUwB,UAAYzK,KAAK8L,UAC3B9L,KAAK+L,aAAa9K,EAAAE,KAAA,EACZnB,KAAK4U,aAAY,wBAAA3T,EAAAsB,OAAA,EAAA/B,EAAA,SACxB,SAJWgC,GAAA,OAAAqJ,EAAApJ,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,MAMZ,WACE,MAAO,kpPAyST,GAAC,CAAAD,IAAA,aAAAC,MAED,WAAa,IAAAqI,EAAA,KACLoM,EAAczL,SAASC,eAAe,eACtCoI,EAAerI,SAASC,eAAe,gBACvCqI,EAAgBtI,SAASC,eAAe,iBACxCyL,EAAY1L,SAASC,eAAe,aACpCuI,EAAQxI,SAASC,eAAe,kBAChCwI,EAAgBD,aAAK,EAALA,EAAOrH,cAAc,mBAE3CsK,SAAAA,EAAa1J,iBAAiB,QAAS,kBAAM1C,EAAKsM,eAAe,GACjEtD,SAAAA,EAActG,iBAAiB,QAAS,kBAAM1C,EAAKuM,eAAe,GAClEtD,SAAAA,EAAevG,iBAAiB,QAAS,kBAAM1C,EAAKuM,eAAe,GACnEnD,SAAAA,EAAe1G,iBAAiB,QAAS,kBAAM1C,EAAKuM,eAAe,GACnEF,SAAAA,EAAW3J,iBAAiB,SAAU,SAACkB,GAAC,OAAK5D,EAAKuJ,iBAAiB3F,EAAE,EACvE,GAAC,CAAAlM,IAAA,aAAAC,OAAA6U,GAAA3U,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAoC,IAAA,IAAA/B,EAAA,OAAAL,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,cAAA2B,EAAA5B,KAAA,EAAA4B,EAAA3B,KAAA,EAE2BnB,KAAKwG,WAAW5E,IAAI,WAAU,OAA/ChB,EAAQkC,EAAAnB,KACd3B,KAAK4P,OAAShP,EAASE,MAAQ,GAC/Bd,KAAKkV,mBAAmBpS,EAAA3B,KAAA,eAAA2B,EAAA5B,KAAA,EAAA4B,EAAA,SAGxB9C,KAAK0L,oBAAoB3C,MAAM,QAAS,yBACxC/I,KAAKuS,cAAc,wBAAAzP,EAAAP,OAAA,EAAAI,EAAA,iBAEtB,WAVe,OAAAsS,EAAAxS,MAAC,KAADzB,UAAA,KAAAb,IAAA,mBAAAC,MAYhB,WAAmB,IAAAyI,EAAA,KACXI,EAAYG,SAASC,eAAe,cAC1C,GAAKJ,EAEL,GAA2B,IAAvBjJ,KAAK4P,OAAOxO,OAAhB,CAWA,IAAM+T,EAAYnV,KAAK4P,OAAO6C,IAAI,SAAAa,GAAK,wFAAAhS,OAG/BgS,EAAM8B,YAAc,aAAH9T,OACJgS,EAAM8B,YAAW,WAAA9T,OAAUuH,EAAKqC,WAAWoI,EAAMX,MAAK,4PAEN,4FAAArR,OAItCuH,EAAKqC,WAAWoI,EAAMX,MAAK,oEAAArR,OAEvC,IAAIsG,KAAK0L,EAAMN,YAAYC,qBAAoB,kJAAA3R,OAGegS,EAAMjU,GAAE,sLAAAiC,OAIJgS,EAAMjU,GAAE,wJAO1F6T,KAAK,IAERjK,EAAUwB,UAAY0K,EAGtBlI,OAAOoI,WAAarV,IAjCpB,MAREiJ,EAAUwB,UAAY,sNA0C1B,GAAC,CAAAtK,IAAA,cAAAC,MAED,WACE,IAAM6I,EAAYG,SAASC,eAAe,cACtCJ,IACFA,EAAUwB,UAAY,sUAS1B,GAAC,CAAAtK,IAAA,gBAAAC,MAED,WAA4B,IAAdkT,EAAKtS,UAAAI,OAAA,QAAAC,IAAAL,UAAA,GAAAA,UAAA,GAAG,KACpBhB,KAAK2U,aAAerB,EACpB,IAAM1B,EAAQxI,SAASC,eAAe,kBAChC4C,EAAO7C,SAASC,eAAe,aAC/BQ,EAAQT,SAASC,eAAe,aAElCiK,GACFzJ,EAAMJ,YAAc,aACpBzJ,KAAKwT,aAAaF,KAElBzJ,EAAMJ,YAAc,gBACpBwC,EAAKwH,SAGP7B,EAAMrI,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,QACjC,GAAC,CAAAxT,IAAA,gBAAAC,MAED,WACgBgJ,SAASC,eAAe,kBAChCE,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,GAC/B3T,KAAK2U,aAAe,IACtB,GAAC,CAAAxU,IAAA,eAAAC,MAED,SAAakT,GACXlK,SAASC,eAAe,aAAajJ,MAAQkT,EAAMX,MAAQ,GAC3DvJ,SAASC,eAAe,cAAcjJ,MAAQkT,EAAM8B,aAAe,EACrE,GAAC,CAAAjV,IAAA,mBAAAC,OAAA0T,GAAAxT,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAA8C,EAAuBgJ,GAAC,IAAA0H,EAAAC,EAAAC,EAAA1P,EAAA+Q,EAAA9N,EAAA,OAAAjH,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,OAegB,GAdtCkL,EAAEC,iBAEIyH,EAAU3K,SAASC,eAAe,gBAClC2K,EAAUD,EAAQxJ,cAAc,aAChC0J,EAAaF,EAAQxJ,cAAc,gBAEnChG,EAAW,IAAI4P,SAAS9H,EAAE+H,QAC1BkB,EAAY,CAChB3C,KAAMpO,EAAS3C,IAAI,QACnBwT,YAAa7Q,EAAS3C,IAAI,gBAAkB,MAG9CmS,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUC,IAAI,UACtBoJ,EAAWrJ,UAAUV,OAAO,UAAU3G,EAAArC,KAAA,GAGhClB,KAAK2U,aAAc,CAAFpR,EAAApC,KAAA,eAAAoC,EAAApC,KAAA,EACbnB,KAAKwG,WAAWV,IAAI,WAADxE,OAAYtB,KAAK2U,aAAatV,IAAMiW,GAAU,OACvEtV,KAAK0L,oBAAoBzE,QAAQ,UAAW,8BAA8B1D,EAAApC,KAAA,sBAAAoC,EAAApC,KAAA,EAEpEnB,KAAKwG,WAAWhB,KAAK,UAAW8P,GAAU,OAChDtV,KAAK0L,oBAAoBzE,QAAQ,UAAW,8BAA8B,OAGvD,OAArBjH,KAAKgV,gBAAgBzR,EAAApC,KAAA,EACfnB,KAAK4U,aAAY,OAAArR,EAAApC,KAAA,eAAAoC,EAAArC,KAAA,EAAAsG,EAAAjE,EAAA,SAIvBvD,KAAK0L,oBAAoB3C,MAAM,QAASvB,EAAMtF,SAAW,wBAAwB,OAI9C,OAJ8CqB,EAAArC,KAAA,EAEjF6S,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUV,OAAO,UACzB+J,EAAWrJ,UAAUC,IAAI,UAAUtH,EAAAyE,OAAA,2BAAAzE,EAAAhB,OAAA,EAAAc,EAAA,qBAEtC,SArCqBF,GAAA,OAAA2Q,EAAArR,MAAC,KAADzB,UAAA,KAAAb,IAAA,YAAAC,MAuCtB,SAAUf,GACR,IAAMiU,EAAQtT,KAAK4P,OAAOyE,KAAK,SAAAkB,GAAC,OAAIA,EAAElW,KAAOA,CAAE,GAC3CiU,GACFtT,KAAK+U,cAAczB,EAEvB,GAAC,CAAAnT,IAAA,cAAAC,OAAAoV,GAAAlV,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAsD,EAAkBxE,GAAE,IAAAiU,EAAAkB,EAAA,OAAAjU,IAAAA,KAAA,SAAAwD,GAAA,cAAAA,EAAA7C,KAAA6C,EAAA5C,MAAA,OAC8B,GAA1CmS,EAAQtT,KAAK4P,OAAOyE,KAAK,SAAAkB,GAAC,OAAIA,EAAElW,KAAOA,CAAE,GACnC,CAAF0E,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,oBAELmS,QAAQ,oCAADnT,OAAqCgS,EAAMX,KAAI,qCAAqC,CAAF5O,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,wBAAAyB,EAAA7C,KAAA,EAAA6C,EAAA5C,KAAA,EAKtFnB,KAAKwG,WAAU,OAAQ,WAADlF,OAAYjC,IAAK,OAC6B,OAA1EW,KAAK0L,oBAAoBzE,QAAQ,UAAW,8BAA8BlD,EAAA5C,KAAA,EACpEnB,KAAK4U,aAAY,OAAA7Q,EAAA5C,KAAA,eAAA4C,EAAA7C,KAAA,EAAAsT,EAAAzQ,EAAA,SAGvB/D,KAAK0L,oBAAoB3C,MAAM,QAASyL,EAAMtS,SAAW,0BAA0B,wBAAA6B,EAAAxB,OAAA,EAAAsB,EAAA,iBAEtF,SAhBgBF,GAAA,OAAA6R,EAAA/S,MAAC,KAADzB,UAAA,KAAAb,IAAA,aAAAC,MAkBjB,SAAW2B,GACT,IAAMwJ,EAAMnC,SAASI,cAAc,OAEnC,OADA+B,EAAI9B,YAAc1H,EACXwJ,EAAId,SACb,KAxBC,IAAA+K,EA9CA1B,EAzGAmB,EAjUApJ,CAgfA,CAvfa,GC8fhB,QA9foB,WAOjB,OAAA/L,EAAAA,EAAAA,GAND,SAAA2V,KAAc1V,EAAAA,EAAAA,GAAA,KAAA0V,GACZzV,KAAKwG,WAAa,IAAI3G,EACtBG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAK6P,WAAa,GAClB7P,KAAKqR,UAAW,EAChBrR,KAAK0V,gBAAkB,IACzB,EAAC,EAAAvV,IAAA,SAAAC,OAAAyL,GAAAvL,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAayI,GAAS,OAAA1I,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAEF,OADlB8H,EAAUwB,UAAYzK,KAAK8L,UAC3B9L,KAAK+L,aAAa9K,EAAAE,KAAA,EACZnB,KAAK2V,iBAAgB,wBAAA1U,EAAAsB,OAAA,EAAA/B,EAAA,SAC5B,SAJWgC,GAAA,OAAAqJ,EAAApJ,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,MAMZ,WACE,MAAO,g7PA6ST,GAAC,CAAAD,IAAA,aAAAC,MAED,WAAa,IAAAqI,EAAA,KACLmN,EAAiBxM,SAASC,eAAe,kBACzCoI,EAAerI,SAASC,eAAe,gBACvCqI,EAAgBtI,SAASC,eAAe,iBACxCwM,EAAezM,SAASC,eAAe,gBACvCuI,EAAQxI,SAASC,eAAe,qBAChCwI,EAAgBD,aAAK,EAALA,EAAOrH,cAAc,mBAE3CqL,SAAAA,EAAgBzK,iBAAiB,QAAS,kBAAM1C,EAAKqN,kBAAkB,GACvErE,SAAAA,EAActG,iBAAiB,QAAS,kBAAM1C,EAAKsN,kBAAkB,GACrErE,SAAAA,EAAevG,iBAAiB,QAAS,kBAAM1C,EAAKsN,kBAAkB,GACtElE,SAAAA,EAAe1G,iBAAiB,QAAS,kBAAM1C,EAAKsN,kBAAkB,GACtEF,SAAAA,EAAc1K,iBAAiB,SAAU,SAACkB,GAAC,OAAK5D,EAAKuJ,iBAAiB3F,EAAE,EAC1E,GAAC,CAAAlM,IAAA,iBAAAC,OAAA4V,GAAA1V,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAoC,IAAA,IAAA/B,EAAA,OAAAL,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,cAAA2B,EAAA5B,KAAA,EAAA4B,EAAA3B,KAAA,EAE2BnB,KAAKwG,WAAW5E,IAAI,eAAc,OAAnDhB,EAAQkC,EAAAnB,KACd3B,KAAK6P,WAAajP,EAASE,MAAQ,GACnCd,KAAKiW,uBAAuBnT,EAAA3B,KAAA,eAAA2B,EAAA5B,KAAA,EAAA4B,EAAA,SAG5B9C,KAAK0L,oBAAoB3C,MAAM,QAAS,6BACxC/I,KAAKuS,cAAc,wBAAAzP,EAAAP,OAAA,EAAAI,EAAA,iBAEtB,WAVmB,OAAAqT,EAAAvT,MAAC,KAADzB,UAAA,KAAAb,IAAA,uBAAAC,MAYpB,WAAuB,IAAAyI,EAAA,KACfI,EAAYG,SAASC,eAAe,kBAC1C,GAAKJ,EAEL,GAA+B,IAA3BjJ,KAAK6P,WAAWzO,OAApB,CAWA,IAAM+T,EAAYnV,KAAK6P,WAAW4C,IAAI,SAAAc,GAAQ,8FAAAjS,OAGtCiS,EAAS2C,eAAiB,aAAH5U,OACViS,EAAS2C,eAAc,WAAA5U,OAAUuH,EAAKqC,WAAWqI,EAASZ,MAAK,8PAEd,kGAAArR,OAIpCuH,EAAKqC,WAAWqI,EAASZ,MAAK,uEAAArR,OAE7C,IAAIsG,KAAK2L,EAASP,YAAYC,qBAAoB,4JAAA3R,OAGmBiS,EAASlU,GAAE,6LAAAiC,OAIPiS,EAASlU,GAAE,wJAOpG6T,KAAK,IAERjK,EAAUwB,UAAY0K,EAGtBlI,OAAOkJ,eAAiBnW,IAjCxB,MAREiJ,EAAUwB,UAAY,6NA0C1B,GAAC,CAAAtK,IAAA,cAAAC,MAED,WACE,IAAM6I,EAAYG,SAASC,eAAe,kBACtCJ,IACFA,EAAUwB,UAAY,8UAS1B,GAAC,CAAAtK,IAAA,mBAAAC,MAED,WAAkC,IAAjBmT,EAAQvS,UAAAI,OAAA,QAAAC,IAAAL,UAAA,GAAAA,UAAA,GAAG,KAC1BhB,KAAK0V,gBAAkBnC,EACvB,IAAM3B,EAAQxI,SAASC,eAAe,qBAChC4C,EAAO7C,SAASC,eAAe,gBAC/BQ,EAAQT,SAASC,eAAe,aAElCkK,GACF1J,EAAMJ,YAAc,gBACpBzJ,KAAKwT,aAAaD,KAElB1J,EAAMJ,YAAc,mBACpBwC,EAAKwH,SAGP7B,EAAMrI,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,QACjC,GAAC,CAAAxT,IAAA,mBAAAC,MAED,WACgBgJ,SAASC,eAAe,qBAChCE,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,GAC/B3T,KAAK0V,gBAAkB,IACzB,GAAC,CAAAvV,IAAA,eAAAC,MAED,SAAamT,GACXnK,SAASC,eAAe,gBAAgBjJ,MAAQmT,EAASZ,MAAQ,GACjEvJ,SAASC,eAAe,iBAAiBjJ,MAAQmT,EAAS2C,gBAAkB,EAC9E,GAAC,CAAA/V,IAAA,mBAAAC,OAAA0T,GAAAxT,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAA8C,EAAuBgJ,GAAC,IAAA0H,EAAAC,EAAAC,EAAA1P,EAAA6R,EAAA5O,EAAA,OAAAjH,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,OAegB,GAdtCkL,EAAEC,iBAEIyH,EAAU3K,SAASC,eAAe,mBAClC2K,EAAUD,EAAQxJ,cAAc,aAChC0J,EAAaF,EAAQxJ,cAAc,gBAEnChG,EAAW,IAAI4P,SAAS9H,EAAE+H,QAC1BgC,EAAe,CACnBzD,KAAMpO,EAAS3C,IAAI,QACnBsU,eAAgB3R,EAAS3C,IAAI,mBAAqB,MAGpDmS,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUC,IAAI,UACtBoJ,EAAWrJ,UAAUV,OAAO,UAAU3G,EAAArC,KAAA,GAGhClB,KAAK0V,gBAAiB,CAAFnS,EAAApC,KAAA,eAAAoC,EAAApC,KAAA,EAChBnB,KAAKwG,WAAWV,IAAI,eAADxE,OAAgBtB,KAAK0V,gBAAgBrW,IAAM+W,GAAa,OACjFpW,KAAK0L,oBAAoBzE,QAAQ,UAAW,iCAAiC1D,EAAApC,KAAA,sBAAAoC,EAAApC,KAAA,EAEvEnB,KAAKwG,WAAWhB,KAAK,cAAe4Q,GAAa,OACvDpW,KAAK0L,oBAAoBzE,QAAQ,UAAW,iCAAiC,OAGvD,OAAxBjH,KAAK+V,mBAAmBxS,EAAApC,KAAA,EAClBnB,KAAK2V,iBAAgB,OAAApS,EAAApC,KAAA,eAAAoC,EAAArC,KAAA,EAAAsG,EAAAjE,EAAA,SAI3BvD,KAAK0L,oBAAoB3C,MAAM,QAASvB,EAAMtF,SAAW,2BAA2B,OAIjD,OAJiDqB,EAAArC,KAAA,EAEpF6S,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUV,OAAO,UACzB+J,EAAWrJ,UAAUC,IAAI,UAAUtH,EAAAyE,OAAA,2BAAAzE,EAAAhB,OAAA,EAAAc,EAAA,qBAEtC,SArCqBF,GAAA,OAAA2Q,EAAArR,MAAC,KAADzB,UAAA,KAAAb,IAAA,eAAAC,MAuCtB,SAAaf,GACX,IAAMkU,EAAWvT,KAAK6P,WAAWwE,KAAK,SAAAgC,GAAC,OAAIA,EAAEhX,KAAOA,CAAE,GAClDkU,GACFvT,KAAK8V,iBAAiBvC,EAE1B,GAAC,CAAApT,IAAA,iBAAAC,OAAAkW,GAAAhW,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAsD,EAAqBxE,GAAE,IAAAkU,EAAAiB,EAAA,OAAAjU,IAAAA,KAAA,SAAAwD,GAAA,cAAAA,EAAA7C,KAAA6C,EAAA5C,MAAA,OACkC,GAAjDoS,EAAWvT,KAAK6P,WAAWwE,KAAK,SAAAgC,GAAC,OAAIA,EAAEhX,KAAOA,CAAE,GACvC,CAAF0E,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,oBAERmS,QAAQ,oCAADnT,OAAqCiS,EAASZ,KAAI,qCAAqC,CAAF5O,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,wBAAAyB,EAAA7C,KAAA,EAAA6C,EAAA5C,KAAA,EAKzFnB,KAAKwG,WAAU,OAAQ,eAADlF,OAAgBjC,IAAK,OAC4B,OAA7EW,KAAK0L,oBAAoBzE,QAAQ,UAAW,iCAAiClD,EAAA5C,KAAA,EACvEnB,KAAK2V,iBAAgB,OAAA5R,EAAA5C,KAAA,eAAA4C,EAAA7C,KAAA,EAAAsT,EAAAzQ,EAAA,SAG3B/D,KAAK0L,oBAAoB3C,MAAM,QAASyL,EAAMtS,SAAW,6BAA6B,wBAAA6B,EAAAxB,OAAA,EAAAsB,EAAA,iBAEzF,SAhBmBF,GAAA,OAAA2S,EAAA7T,MAAC,KAADzB,UAAA,KAAAb,IAAA,aAAAC,MAkBpB,SAAW2B,GACT,IAAMwJ,EAAMnC,SAASI,cAAc,OAEnC,OADA+B,EAAI9B,YAAc1H,EACXwJ,EAAId,SACb,KAxBC,IAAA6L,EA9CAxC,EAzGAkC,EArUAnK,CAofA,CA3fiB,G,+jBCslBpB,QAtlBiB,WAOd,OAAA/L,EAAAA,EAAAA,GAND,SAAAyW,KAAcxW,EAAAA,EAAAA,GAAA,KAAAwW,GACZvW,KAAKwG,WAAa,IAAI3G,EACtBG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAK8P,QAAU,GACf9P,KAAKqR,UAAW,EAChBrR,KAAKwW,cAAgB,IACvB,EAAC,EAAArW,IAAA,SAAAC,OAAAyL,GAAAvL,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAayI,GAAS,OAAA1I,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAEF,OADlB8H,EAAUwB,UAAYzK,KAAK8L,UAC3B9L,KAAK+L,aAAa9K,EAAAE,KAAA,EACZnB,KAAKyW,cAAa,wBAAAxV,EAAAsB,OAAA,EAAA/B,EAAA,SACzB,SAJWgC,GAAA,OAAAqJ,EAAApJ,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,MAMZ,WACE,MAAO,m0SAmWT,GAAC,CAAAD,IAAA,aAAAC,MAED,WAAa,IAAAqI,EAAA,KACLiO,EAAetN,SAASC,eAAe,gBACvCoI,EAAerI,SAASC,eAAe,gBACvCqI,EAAgBtI,SAASC,eAAe,iBACxCsN,EAAavN,SAASC,eAAe,cACrCuI,EAAQxI,SAASC,eAAe,mBAChCwI,EAAgBD,aAAK,EAALA,EAAOrH,cAAc,mBAE3CmM,SAAAA,EAAcvL,iBAAiB,QAAS,kBAAM1C,EAAKmO,gBAAgB,GACnEnF,SAAAA,EAActG,iBAAiB,QAAS,kBAAM1C,EAAKoO,gBAAgB,GACnEnF,SAAAA,EAAevG,iBAAiB,QAAS,kBAAM1C,EAAKoO,gBAAgB,GACpEhF,SAAAA,EAAe1G,iBAAiB,QAAS,kBAAM1C,EAAKoO,gBAAgB,GACpEF,SAAAA,EAAYxL,iBAAiB,SAAU,SAACkB,GAAC,OAAK5D,EAAKuJ,iBAAiB3F,EAAE,EACxE,GAAC,CAAAlM,IAAA,cAAAC,OAAA0W,GAAAxW,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAoC,IAAA,IAAA/B,EAAA,OAAAL,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,cAAA2B,EAAA5B,KAAA,EAAA4B,EAAA3B,KAAA,EAE2BnB,KAAKwG,WAAW5E,IAAI,YAAW,OAAhDhB,EAAQkC,EAAAnB,KACd3B,KAAK8P,QAAUlP,EAASE,MAAQ,GAChCd,KAAK+W,oBAAoBjU,EAAA3B,KAAA,eAAA2B,EAAA5B,KAAA,EAAA4B,EAAA,SAGzB9C,KAAK0L,oBAAoB3C,MAAM,QAAS,0BACxC/I,KAAKuS,cAAc,wBAAAzP,EAAAP,OAAA,EAAAI,EAAA,iBAEtB,WAVgB,OAAAmU,EAAArU,MAAC,KAADzB,UAAA,KAAAb,IAAA,oBAAAC,MAYjB,WAAoB,IAAAyI,EAAA,KACZI,EAAYG,SAASC,eAAe,eAC1C,GAAKJ,EAEL,GAA4B,IAAxBjJ,KAAK8P,QAAQ1O,OAAjB,CAWA,IAAM+T,EAAYnV,KAAK8P,QAAQ2C,IAAI,SAAAuE,GAAM,4FAAA1V,OAGjC0V,EAAOC,iBAAmB,aAAH3V,OACV0V,EAAOC,iBAAgB,WAAA3V,OAAUuH,EAAKqC,WAAW8L,EAAOnN,OAAM,4PAEd,0CAAAvI,OAEnC0V,EAAOE,OAAS,SAAW,WAAU,oBAAA5V,OAC7D0V,EAAOE,OAAS,SAAW,WAAU,iHAAA5V,OAIduH,EAAKqC,WAAW8L,EAAOnN,OAAM,qEAAAvI,OAE3C,IAAIsG,KAAKoP,EAAOhE,YAAYC,qBAAoB,kCAAA3R,OAE3D0V,EAAOG,aAAe,YAAH7V,OACP0V,EAAOG,aAAY,yCAAA7V,OAAwC0V,EAAOG,aAAY,QAC1F,GAAE,mIAAA7V,OAGyE0V,EAAO3X,GAAE,wLAAAiC,OAIL0V,EAAO3X,GAAE,wCAAAiC,OACnE0V,EAAOE,OAAS,YAAc,MAAK,0BAAA5V,OACpD0V,EAAOE,OAAS,aAAe,WAAU,sHAAA5V,OAEkC0V,EAAO3X,GAAE,wJAO7F6T,KAAK,IAERjK,EAAUwB,UAAY0K,EAGtBlI,OAAOmK,YAAcpX,IA5CrB,MAREiJ,EAAUwB,UAAY,kOAqD1B,GAAC,CAAAtK,IAAA,cAAAC,MAED,WACE,IAAM6I,EAAYG,SAASC,eAAe,eACtCJ,IACFA,EAAUwB,UAAY,wUAS1B,GAAC,CAAAtK,IAAA,iBAAAC,MAED,WAA8B,IAAf4W,EAAMhW,UAAAI,OAAA,QAAAC,IAAAL,UAAA,GAAAA,UAAA,GAAG,KACtBhB,KAAKwW,cAAgBQ,EACrB,IAAMpF,EAAQxI,SAASC,eAAe,mBAChC4C,EAAO7C,SAASC,eAAe,cAC/BQ,EAAQT,SAASC,eAAe,aAElC2N,GACFnN,EAAMJ,YAAc,cACpBzJ,KAAKwT,aAAawD,KAElBnN,EAAMJ,YAAc,iBACpBwC,EAAKwH,QACLrK,SAASC,eAAe,gBAAgBgO,SAAU,GAGpDzF,EAAMrI,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,QACjC,GAAC,CAAAxT,IAAA,iBAAAC,MAED,WACgBgJ,SAASC,eAAe,mBAChCE,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,GAC/B3T,KAAKwW,cAAgB,IACvB,GAAC,CAAArW,IAAA,eAAAC,MAED,SAAa4W,GACX5N,SAASC,eAAe,eAAejJ,MAAQ4W,EAAOnN,OAAS,GAC/DT,SAASC,eAAe,eAAejJ,MAAQ4W,EAAOC,kBAAoB,GAC1E7N,SAASC,eAAe,kBAAkBjJ,MAAQ4W,EAAOG,cAAgB,GACzE/N,SAASC,eAAe,gBAAgBgO,QAAUL,EAAOE,MAC3D,GAAC,CAAA/W,IAAA,mBAAAC,OAAA0T,GAAAxT,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAA8C,EAAuBgJ,GAAC,IAAA0H,EAAAC,EAAAC,EAAA1P,EAAA+S,EAAA9P,EAAA,OAAAjH,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,OAiBgB,GAhBtCkL,EAAEC,iBAEIyH,EAAU3K,SAASC,eAAe,iBAClC2K,EAAUD,EAAQxJ,cAAc,aAChC0J,EAAaF,EAAQxJ,cAAc,gBAEnChG,EAAW,IAAI4P,SAAS9H,EAAE+H,QAC1BkD,EAAa,CACjBzN,MAAOtF,EAAS3C,IAAI,SACpBqV,iBAAkB1S,EAAS3C,IAAI,oBAC/BuV,aAAc5S,EAAS3C,IAAI,iBAAmB,KAC9CsV,OAAQ3S,EAASgT,IAAI,WAGvBxD,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUC,IAAI,UACtBoJ,EAAWrJ,UAAUV,OAAO,UAAU3G,EAAArC,KAAA,GAGhClB,KAAKwW,cAAe,CAAFjT,EAAApC,KAAA,eAAAoC,EAAApC,KAAA,EACdnB,KAAKwG,WAAWV,IAAI,YAADxE,OAAatB,KAAKwW,cAAcnX,IAAMiY,GAAW,OAC1EtX,KAAK0L,oBAAoBzE,QAAQ,UAAW,+BAA+B1D,EAAApC,KAAA,sBAAAoC,EAAApC,KAAA,EAErEnB,KAAKwG,WAAWhB,KAAK,WAAY8R,GAAW,OAClDtX,KAAK0L,oBAAoBzE,QAAQ,UAAW,+BAA+B,OAGvD,OAAtBjH,KAAK6W,iBAAiBtT,EAAApC,KAAA,EAChBnB,KAAKyW,cAAa,OAAAlT,EAAApC,KAAA,eAAAoC,EAAArC,KAAA,EAAAsG,EAAAjE,EAAA,SAIxBvD,KAAK0L,oBAAoB3C,MAAM,QAASvB,EAAMtF,SAAW,yBAAyB,OAI/C,OAJ+CqB,EAAArC,KAAA,EAElF6S,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUV,OAAO,UACzB+J,EAAWrJ,UAAUC,IAAI,UAAUtH,EAAAyE,OAAA,2BAAAzE,EAAAhB,OAAA,EAAAc,EAAA,qBAEtC,SAvCqBF,GAAA,OAAA2Q,EAAArR,MAAC,KAADzB,UAAA,KAAAb,IAAA,aAAAC,MAyCtB,SAAWf,GACT,IAAM2X,EAAShX,KAAK8P,QAAQuE,KAAK,SAAAkB,GAAC,OAAIA,EAAElW,KAAOA,CAAE,GAC7C2X,GACFhX,KAAK4W,eAAeI,EAExB,GAAC,CAAA7W,IAAA,eAAAC,OAAAoX,GAAAlX,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAsD,EAAmBxE,GAAE,IAAA2X,EAAAxC,EAAA,OAAAjU,IAAAA,KAAA,SAAAwD,GAAA,cAAAA,EAAA7C,KAAA6C,EAAA5C,MAAA,OAC+B,GAA5C6V,EAAShX,KAAK8P,QAAQuE,KAAK,SAAAkB,GAAC,OAAIA,EAAElW,KAAOA,CAAE,GACpC,CAAF0E,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,wBAAAyB,EAAA7C,KAAA,EAAA6C,EAAA5C,KAAA,EAGHnB,KAAKwG,WAAWV,IAAI,YAADxE,OAAajC,GAAEkC,EAAAA,EAAA,GACnCyV,GAAM,IACTE,QAASF,EAAOE,UAChB,OAEgH,OAAlHlX,KAAK0L,oBAAoBzE,QAAQ,UAAW,UAAF3F,OAAY0V,EAAOE,OAAS,cAAgB,YAAW,kBAAiBnT,EAAA5C,KAAA,EAC5GnB,KAAKyW,cAAa,OAAA1S,EAAA5C,KAAA,eAAA4C,EAAA7C,KAAA,EAAAsT,EAAAzQ,EAAA,SAGxB/D,KAAK0L,oBAAoB3C,MAAM,QAASyL,EAAMtS,SAAW,kCAAkC,wBAAA6B,EAAAxB,OAAA,EAAAsB,EAAA,iBAE9F,SAhBiBF,GAAA,OAAA6T,EAAA/U,MAAC,KAADzB,UAAA,KAAAb,IAAA,eAAAC,OAAAqX,GAAAnX,EAAAA,EAAAA,GAAAC,IAAAA,KAkBlB,SAAA2D,EAAmB7E,GAAE,IAAA2X,EAAAU,EAAA,OAAAnX,IAAAA,KAAA,SAAA4D,GAAA,cAAAA,EAAAjD,KAAAiD,EAAAhD,MAAA,OAC+B,GAA5C6V,EAAShX,KAAK8P,QAAQuE,KAAK,SAAAkB,GAAC,OAAIA,EAAElW,KAAOA,CAAE,GACpC,CAAF8E,EAAAhD,KAAA,eAAAgD,EAAA7B,OAAA,oBAENmS,QAAQ,oCAADnT,OAAqC0V,EAAOnN,MAAK,qCAAqC,CAAF1F,EAAAhD,KAAA,eAAAgD,EAAA7B,OAAA,wBAAA6B,EAAAjD,KAAA,EAAAiD,EAAAhD,KAAA,EAKxFnB,KAAKwG,WAAU,OAAQ,YAADlF,OAAajC,IAAK,OAC6B,OAA3EW,KAAK0L,oBAAoBzE,QAAQ,UAAW,+BAA+B9C,EAAAhD,KAAA,EACrEnB,KAAKyW,cAAa,OAAAtS,EAAAhD,KAAA,eAAAgD,EAAAjD,KAAA,EAAAwW,EAAAvT,EAAA,SAGxBnE,KAAK0L,oBAAoB3C,MAAM,QAAS2O,EAAMxV,SAAW,2BAA2B,wBAAAiC,EAAA5B,OAAA,EAAA2B,EAAA,iBAEvF,SAhBiBF,GAAA,OAAAyT,EAAAhV,MAAC,KAADzB,UAAA,KAAAb,IAAA,aAAAC,MAkBlB,SAAW2B,GACT,IAAMwJ,EAAMnC,SAASI,cAAc,OAEnC,OADA+B,EAAI9B,YAAc1H,EACXwJ,EAAId,SACb,KAxCkB,IAAAgN,EAFjBD,EAhDA1D,EAvHAgD,EA3XAjL,CA4kBA,CAnlBc,GCskBjB,QAtkByB,WAOtB,OAAA/L,EAAAA,EAAAA,GAND,SAAA6X,KAAc5X,EAAAA,EAAAA,GAAA,KAAA4X,GACZ3X,KAAKwG,WAAa,IAAI3G,EACtBG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAK4X,OAAS,GACd5X,KAAKqR,UAAW,EAChBrR,KAAK6X,aAAe,IACtB,EAAC,EAAA1X,IAAA,SAAAC,OAAAyL,GAAAvL,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,EAAayI,GAAS,OAAA1I,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAEF,OADlB8H,EAAUwB,UAAYzK,KAAK8L,UAC3B9L,KAAK+L,aAAa9K,EAAAE,KAAA,EACZnB,KAAK8X,aAAY,wBAAA7W,EAAAsB,OAAA,EAAA/B,EAAA,SACxB,SAJWgC,GAAA,OAAAqJ,EAAApJ,MAAC,KAADzB,UAAA,KAAAb,IAAA,UAAAC,MAMZ,WACE,MAAO,0jSAyVT,GAAC,CAAAD,IAAA,aAAAC,MAED,WAAa,IAAAqI,EAAA,KACLsP,EAAc3O,SAASC,eAAe,eACtCoI,EAAerI,SAASC,eAAe,gBACvCqI,EAAgBtI,SAASC,eAAe,iBACxC2O,EAAY5O,SAASC,eAAe,aACpCuI,EAAQxI,SAASC,eAAe,kBAChCwI,EAAgBD,aAAK,EAALA,EAAOrH,cAAc,mBACrC2B,EAAiB9C,SAASC,eAAe,kBACzC8C,EAAgB/C,SAASC,eAAe,iBACxC+C,EAAqBhD,SAASC,eAAe,sBAEnD0O,SAAAA,EAAa5M,iBAAiB,QAAS,kBAAM1C,EAAKwP,eAAe,GACjExG,SAAAA,EAActG,iBAAiB,QAAS,kBAAM1C,EAAKyP,eAAe,GAClExG,SAAAA,EAAevG,iBAAiB,QAAS,kBAAM1C,EAAKyP,eAAe,GACnErG,SAAAA,EAAe1G,iBAAiB,QAAS,kBAAM1C,EAAKyP,eAAe,GACnEF,SAAAA,EAAW7M,iBAAiB,SAAU,SAACkB,GAAC,OAAK5D,EAAKuJ,iBAAiB3F,EAAE,GAGrEH,SAAAA,EAAgBf,iBAAiB,QAAS,WACxC,IAAMgN,EAAoC,aAAvBhM,EAAcvC,KACjCuC,EAAcvC,KAAOuO,EAAa,OAAS,WAC3C/L,EAAmB5B,UAAY2N,EAAa,mBAAqB,YACnE,EACF,GAAC,CAAAhY,IAAA,aAAAC,OAAAgY,GAAA9X,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAoC,IAAA,IAAA/B,EAAA,OAAAL,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,cAAA2B,EAAA5B,KAAA,EAAA4B,EAAA3B,KAAA,EAE2BnB,KAAKwG,WAAW5E,IAAI,WAAU,OAA/ChB,EAAQkC,EAAAnB,KACd3B,KAAK4X,OAAShX,EAASE,MAAQ,GAC/Bd,KAAKqY,kBAAkBvV,EAAA3B,KAAA,eAAA2B,EAAA5B,KAAA,EAAA4B,EAAA,SAGvB9C,KAAK0L,oBAAoB3C,MAAM,QAAS,8BACxC/I,KAAKuS,cAAc,wBAAAzP,EAAAP,OAAA,EAAAI,EAAA,iBAEtB,WAVe,OAAAyV,EAAA3V,MAAC,KAADzB,UAAA,KAAAb,IAAA,kBAAAC,MAYhB,WAAkB,IAAAyI,EAAA,KACVI,EAAYG,SAASC,eAAe,aAC1C,GAAKJ,EAEL,GAA2B,IAAvBjJ,KAAK4X,OAAOxW,OAAhB,CAWA,IAAMkX,EAActY,KAAKuY,iBACnB/F,EAAY,uQAAHlR,OAWPtB,KAAK4X,OAAOnF,IAAI,SAAA+F,GAAK,kCAAAlX,OACRgX,GAAeA,EAAYjR,WAAamR,EAAMnR,SAAW,eAAiB,GAAE,wIAAA/F,OAI/EkX,EAAMnR,SAASoR,OAAO,GAAGC,cAAa,uGAAApX,OAGlCuH,EAAKqC,WAAWsN,EAAMnR,UAAS,kCAAA/F,OAChCgX,GAAeA,EAAYjR,WAAamR,EAAMnR,SAAW,eAAiB,gBAAe,mGAAA/F,OAI9F,IAAIsG,KAAK4Q,EAAMxF,YAAYC,qBAAoB,6BAAA3R,OAC/C,IAAIsG,KAAK4Q,EAAMG,YAAY1F,qBAAoB,8NAAA3R,OAKGkX,EAAMnZ,GAAE,4BAAAiC,OACxDgX,GAAeA,EAAYjR,WAAamR,EAAMnR,SAAW,8CAAgD,uBAAsB,sLAOxI6L,KAAK,IAAG,4CAKjBjK,EAAUwB,UAAY+H,EAGtBvF,OAAO2L,oBAAsB5Y,IAjD7B,MAREiJ,EAAUwB,UAAY,gOA0D1B,GAAC,CAAAtK,IAAA,cAAAC,MAED,WACE,IAAM6I,EAAYG,SAASC,eAAe,aACtCJ,IACFA,EAAUwB,UAAY,gVAS1B,GAAC,CAAAtK,IAAA,gBAAAC,MAED,WACE,IAAMwR,EAAQxI,SAASC,eAAe,kBACzBD,SAASC,eAAe,aAEhCoK,QACL7B,EAAMrI,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,QACjC,GAAC,CAAAxT,IAAA,gBAAAC,MAED,WACgBgJ,SAASC,eAAe,kBAChCE,MAAMmK,QAAU,OACtBtK,SAAS5F,KAAK+F,MAAMoK,SAAW,EACjC,GAAC,CAAAxT,IAAA,mBAAAC,OAAA0T,GAAAxT,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAA8C,EAAuBgJ,GAAC,IAAA0H,EAAAC,EAAAC,EAAA1P,EAAA8C,EAAAC,EAAAuR,EAAAC,EAAAtR,EAAA,OAAAjH,IAAAA,KAAA,SAAAgD,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,OAYtB,GAXAkL,EAAEC,iBAEIyH,EAAU3K,SAASC,eAAe,gBAClC2K,EAAUD,EAAQxJ,cAAc,aAChC0J,EAAaF,EAAQxJ,cAAc,gBAEnChG,EAAW,IAAI4P,SAAS9H,EAAE+H,QAC1B/M,EAAW9C,EAAS3C,IAAI,YACxB0F,EAAW/C,EAAS3C,IAAI,YACxBiX,EAAkBtU,EAAS3C,IAAI,mBAGjC0F,IAAauR,EAAe,CAAAtV,EAAApC,KAAA,QAC+C,OAA7EnB,KAAK0L,oBAAoB3C,MAAM,mBAAoB,0BAA0BxF,EAAAjB,OAAA,sBAI3EgF,EAASlG,OAAS,GAAC,CAAAmC,EAAApC,KAAA,QAC6E,OAAlGnB,KAAK0L,oBAAoB3C,MAAM,mBAAoB,+CAA+CxF,EAAAjB,OAAA,iBAW9D,OAPhCwW,EAAY,CAChBzR,SAAAA,EACAC,SAAAA,GAGFyM,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUC,IAAI,UACtBoJ,EAAWrJ,UAAUV,OAAO,UAAU3G,EAAArC,KAAA,EAAAqC,EAAApC,KAAA,EAG9BnB,KAAKwG,WAAWhB,KAAK,UAAWsT,GAAU,OAE3B,OADrB9Y,KAAK0L,oBAAoBzE,QAAQ,UAAW,mCAC5CjH,KAAKkY,gBAAgB3U,EAAApC,KAAA,EACfnB,KAAK8X,aAAY,OAAAvU,EAAApC,KAAA,eAAAoC,EAAArC,KAAA,EAAAsG,EAAAjE,EAAA,SAIvBvD,KAAK0L,oBAAoB3C,MAAM,QAASvB,EAAMtF,SAAW,+BAA+B,OAIrD,OAJqDqB,EAAArC,KAAA,EAExF6S,EAAQhH,UAAW,EACnBiH,EAAQpJ,UAAUV,OAAO,UACzB+J,EAAWrJ,UAAUC,IAAI,UAAUtH,EAAAyE,OAAA,2BAAAzE,EAAAhB,OAAA,EAAAc,EAAA,qBAEtC,SA9CqBF,GAAA,OAAA2Q,EAAArR,MAAC,KAADzB,UAAA,KAAAb,IAAA,cAAAC,OAAA2Y,GAAAzY,EAAAA,EAAAA,GAAAC,IAAAA,KAgDtB,SAAAsD,EAAkBxE,GAAE,IAAAmZ,EAAAF,EAAA9D,EAAA,OAAAjU,IAAAA,KAAA,SAAAwD,GAAA,cAAAA,EAAA7C,KAAA6C,EAAA5C,MAAA,OAC8B,GAA1CqX,EAAQxY,KAAK4X,OAAOvD,KAAK,SAAA2E,GAAC,OAAIA,EAAE3Z,KAAOA,CAAE,GACnC,CAAF0E,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,iBAE+B,KAAnCgW,EAActY,KAAKuY,mBACND,EAAYjR,WAAamR,EAAMnR,SAAQ,CAAAtD,EAAA5C,KAAA,QACwB,OAAhFnB,KAAK0L,oBAAoB3C,MAAM,QAAS,wCAAwChF,EAAAzB,OAAA,oBAI7EmS,QAAQ,+CAADnT,OAAgDkX,EAAMnR,SAAQ,qCAAqC,CAAFtD,EAAA5C,KAAA,eAAA4C,EAAAzB,OAAA,wBAAAyB,EAAA7C,KAAA,EAAA6C,EAAA5C,KAAA,EAKrGnB,KAAKwG,WAAU,OAAQ,WAADlF,OAAYjC,IAAK,OACkC,OAA/EW,KAAK0L,oBAAoBzE,QAAQ,UAAW,mCAAmClD,EAAA5C,KAAA,EACzEnB,KAAK8X,aAAY,OAAA/T,EAAA5C,KAAA,eAAA4C,EAAA7C,KAAA,EAAAsT,EAAAzQ,EAAA,SAGvB/D,KAAK0L,oBAAoB3C,MAAM,QAASyL,EAAMtS,SAAW,+BAA+B,wBAAA6B,EAAAxB,OAAA,EAAAsB,EAAA,iBAE3F,SAtBgBF,GAAA,OAAAoV,EAAAtW,MAAC,KAADzB,UAAA,KAAAb,IAAA,iBAAAC,MAwBjB,WAGE,OAAI6M,OAAOxB,aAAewB,OAAOxB,YAAYsD,QACpC9B,OAAOxB,YAAYsD,UAErB,IACT,GAAC,CAAA5O,IAAA,aAAAC,MAED,SAAW2B,GACT,IAAMwJ,EAAMnC,SAASI,cAAc,OAEnC,OADA+B,EAAI9B,YAAc1H,EACXwJ,EAAId,SACb,KArFsB,IAAAsO,EAFrBjF,EA1GAsE,EA3XAvM,CA4jBA,CAnkBsB,G,q/BCkJzB,QA3IY,WAOT,OAAA/L,EAAAA,EAAAA,GAND,SAAAmZ,KAAclZ,EAAAA,EAAAA,GAAA,KAAAkZ,GACZjZ,KAAKkZ,OAAS,IAAIC,IAClBnZ,KAAKiJ,UAAY,KACjBjJ,KAAKoZ,aAAe,KACpBpZ,KAAKyL,YAAc,IAAIlF,EACvBvG,KAAKqZ,aACP,EAAC,EAAAlZ,IAAA,cAAAC,MAED,WAEEJ,KAAKkZ,OAAOI,IAAI,IAAK,CAAEC,UAAW/N,EAAWgO,cAAc,IAC3DxZ,KAAKkZ,OAAOI,IAAI,SAAU,CAAEC,UAAW/N,EAAWgO,cAAc,IAGhExZ,KAAKkZ,OAAOI,IAAI,SAAU,CAAEC,UAAW5L,EAAgB6L,cAAc,EAAMC,aAAc,qBACzFzZ,KAAKkZ,OAAOI,IAAI,mBAAoB,CAAEC,UAAW9J,EAAe+J,cAAc,EAAME,OAAQ,WAC5F1Z,KAAKkZ,OAAOI,IAAI,kBAAmB,CAAEC,UAAWnI,EAAcoI,cAAc,EAAME,OAAQ,WAC1F1Z,KAAKkZ,OAAOI,IAAI,gBAAiB,CAAEC,UAAW7E,EAAY8E,cAAc,EAAME,OAAQ,WACtF1Z,KAAKkZ,OAAOI,IAAI,oBAAqB,CAAEC,UAAW9D,EAAgB+D,cAAc,EAAME,OAAQ,WAC9F1Z,KAAKkZ,OAAOI,IAAI,iBAAkB,CAAEC,UAAWhD,EAAaiD,cAAc,EAAME,OAAQ,WACxF1Z,KAAKkZ,OAAOI,IAAI,gBAAiB,CAAEC,UAAW5B,EAAqB6B,cAAc,EAAME,OAAQ,UACjG,GAAC,CAAAvZ,IAAA,OAAAC,MAED,SAAK6I,GAAW,IAAAR,EAAA,KACdzI,KAAKiJ,UAAYA,EAGjBgE,OAAO9B,iBAAiB,WAAY,WAClC1C,EAAKkR,aACP,GAGA3Z,KAAK2Z,aACP,GAAC,CAAAxZ,IAAA,WAAAC,MAED,SAASwZ,GAAa5Y,UAAAI,OAAA,QAAAC,IAAAL,UAAA,IAAAA,UAAA,GAElBiM,OAAOkC,QAAQ0K,aAAa,CAAC,EAAG,GAAID,GAEpC3M,OAAOkC,QAAQC,UAAU,CAAC,EAAG,GAAIwK,GAEnC5Z,KAAK2Z,aACP,GAAC,CAAAxZ,IAAA,cAAAC,OAAA0Z,GAAAxZ,EAAAA,EAAAA,GAAAC,IAAAA,KAED,SAAAC,IAAA,IAAAoZ,EAAAlL,EAAAqL,EAAAC,EAAAC,EAAAC,EAAAC,EAAA1T,EAAAoG,EAAAhE,EAAA,YAAAtI,IAAAA,KAAA,SAAAU,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OAIE,GAHMyY,EAAO3M,OAAOC,SAASsC,SACzBd,EAAQ1O,KAAKkZ,OAAOtX,IAAIgY,GAGhB,CAAF3Y,EAAAE,KAAA,QAAA4Y,EAAAK,EAC+Bpa,KAAKkZ,QAAMjY,EAAAC,KAAA,EAAA6Y,EAAAM,IAAA,WAAAL,EAAAD,EAAA1P,KAAAiQ,KAAE,CAAFrZ,EAAAE,KAAA,QAAhB,GAAgB8Y,GAAAxJ,EAAAA,EAAAA,GAAAuJ,EAAA5Z,MAAA,GAAtC8Z,EAASD,EAAA,GAAEE,EAAWF,EAAA,IAC5BL,EAAKW,WAAWL,KAAcC,EAAYV,aAAY,CAAAxY,EAAAE,KAAA,QACpC,OAApBuN,EAAQyL,EAAYlZ,EAAAqB,OAAA,qBAAArB,EAAAE,KAAA,eAAAF,EAAAE,KAAA,eAAAF,EAAAC,KAAA,EAAA2L,EAAA5L,EAAA,SAAA8Y,EAAA1N,EAAAQ,GAAA,cAAA5L,EAAAC,KAAA,EAAA6Y,EAAAS,IAAAvZ,EAAA+G,OAAA,aAOrB0G,EAAO,CAAFzN,EAAAE,KAAA,QACsB,OAA9BnB,KAAKya,SAAS,UAAU,GAAMxZ,EAAAqB,OAAA,wBAAArB,EAAAE,KAAA,EAKFnB,KAAKyL,YAAYiP,YAAW,OAArC,GAAfjU,EAAexF,EAAAU,MAEjB+M,EAAM8K,cAAiB/S,EAAe,CAAAxF,EAAAE,KAAA,SACV,OAA9BnB,KAAKya,SAAS,UAAU,GAAMxZ,EAAAqB,OAAA,qBAI3BoM,EAAM8K,eAAgB/S,GAA4B,WAATmT,EAAiB,CAAA3Y,EAAAE,KAAA,SACrB,OAAxCnB,KAAKya,SAAS,oBAAoB,GAAMxZ,EAAAqB,OAAA,sBAKtCoM,EAAM+K,cAAgBG,IAASe,OAAOC,KAAKD,OAAOE,YAAY7a,KAAKkZ,SAAS7E,KAAK,SAAAlU,GAAG,OAAI0I,EAAKqQ,OAAOtX,IAAIzB,KAASuO,CAAK,GAAC,CAAAzN,EAAAE,KAAA,SACjF,OAAxCnB,KAAKya,SAAS/L,EAAM+K,cAAc,GAAMxY,EAAAqB,OAAA,yBAAArB,EAAAE,KAAA,GAKpCnB,KAAK8a,YAAYpM,EAAOkL,GAAK,yBAAA3Y,EAAAsB,OAAA,EAAA/B,EAAA,qBACpC,WAzCgB,OAAAsZ,EAAArX,MAAC,KAADzB,UAAA,KAAAb,IAAA,cAAAC,OAAA2a,GAAAza,EAAAA,EAAAA,GAAAC,IAAAA,KA2CjB,SAAAoC,EAAkB+L,EAAOkL,GAAI,IAAAoB,EAAAzB,EAAA0B,EAAAC,EAAAC,EAAA,OAAA5a,IAAAA,KAAA,SAAAuC,GAAA,cAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,UACtBnB,KAAKiJ,UAAW,CAAFnG,EAAA3B,KAAA,eAAA2B,EAAAR,OAAA,iBAUjB,GAViBQ,EAAA5B,KAAA,EAIjBlB,KAAKiJ,UAAUwB,UAAY,GAGrBuQ,EAAiBtM,EAAM6K,UACvBA,EAAY,IAAIyB,GAGlBtM,EAAMgL,OAAQ,CAAF5W,EAAA3B,KAAA,QACmC,KAA3C8Z,EAAcjb,KAAKkZ,OAAOtX,IAAI8M,EAAMgL,SACzB,CAAF5W,EAAA3B,KAAA,QACsC,OAA7C+Z,EAAkB,IAAID,EAAY1B,UAAWzW,EAAA3B,KAAA,EAC7C+Z,EAAgBlR,OAAOhK,KAAKiJ,WAAU,OAGsB,KAA5DkS,EAAcnb,KAAKiJ,UAAUsB,cAAc,mBAChC,CAAFzH,EAAA3B,KAAA,eAAA2B,EAAA3B,KAAA,EACPoY,EAAUvP,OAAOmR,GAAY,OAAArY,EAAA3B,KAAA,sBAAA2B,EAAA3B,KAAA,EAE7BoY,EAAUvP,OAAOhK,KAAKiJ,WAAU,OAAAnG,EAAA3B,KAAA,sBAAA2B,EAAA3B,KAAA,EAGlCoY,EAAUvP,OAAOhK,KAAKiJ,WAAU,OAAAnG,EAAA3B,KAAA,sBAAA2B,EAAA3B,KAAA,EAGlCoY,EAAUvP,OAAOhK,KAAKiJ,WAAU,OAGxCjJ,KAAKoZ,aAAe,CAAE1K,MAAAA,EAAOkL,KAAAA,EAAML,UAAAA,GAAYzW,EAAA3B,KAAA,iBAAA2B,EAAA5B,KAAA,GAAA4B,EAAA,SAI/C9C,KAAKiJ,UAAUwB,UAAY,ySAMzB,yBAAA3H,EAAAP,OAAA,EAAAI,EAAA,kBAEL,SA5CgBH,EAAAW,GAAA,OAAA4X,EAAAtY,MAAC,KAADzB,UAAA,KAAAb,IAAA,kBAAAC,MA8CjB,WACE,OAAOJ,KAAKoZ,YACd,KA3FiB,IAAA2B,EAFhBjB,CA6FA,CAxIS,GC0BZ,QAhCS,WAMN,OAAAha,EAAAA,EAAAA,GALD,SAAAsb,KAAcrb,EAAAA,EAAAA,GAAA,KAAAqb,GACZpb,KAAKqb,OAAS,IAAIpC,EAClBjZ,KAAKyL,YAAc,IAAIlF,EACvBvG,KAAK0L,oBAAsB,IAAI1C,EAC/BhJ,KAAKsb,MACP,EAAC,EAAAnb,IAAA,OAAAC,MAED,WAAO,IAAAqI,EAAA,KAELzI,KAAK0L,oBAAoB4P,OAGzBrO,OAAO9B,iBAAiB,qBAAsB,SAACoQ,GAE7C9S,EAAKiD,oBAAoB3C,MAAM,+BACjC,GAGA/I,KAAKyL,YAAY+P,kBAAkB,SAAC/U,IAC7BA,GAAmBwG,OAAOC,SAASsC,SAAS+K,WAAW,WAC1D9R,EAAK4S,OAAOZ,SAAS,SAEzB,EACF,GAAC,CAAAta,IAAA,QAAAC,MAED,SAAM6I,GACJjJ,KAAKiJ,UAAYA,EACjBjJ,KAAKqb,OAAOC,KAAKrS,EACnB,IAAC,CA7BM,GCATG,SAAS+B,iBAAiB,mBAAoB,WAC5C,IAAMsQ,EAAOrS,SAASC,eAAe,QACjCoS,IACFA,EAAKhR,UAAY,IACL,IAAI2Q,GACZM,MAAMD,GAEd,E,GCVIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBxa,IAAjBya,EACH,OAAOA,EAAaC,QAGrB,IAAI3c,EAASuc,EAAyBE,GAAY,CACjDxc,GAAIwc,EAEJE,QAAS,CAAC,GAOX,OAHAC,EAAoBH,GAAUzc,EAAQA,EAAO2c,QAASH,GAG/Cxc,EAAO2c,OACf,CAGAH,EAAoBK,EAAID,EjBzBpB/c,EAAW,GACf2c,EAAoBM,EAAI,CAACC,EAAQC,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIxd,EAASmC,OAAQqb,IAAK,CAGzC,IAFA,IAAKL,EAAUC,EAAIC,GAAYrd,EAASwd,GACpCC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAAShb,OAAQub,MACpB,EAAXL,GAAsBC,GAAgBD,IAAa3B,OAAOC,KAAKgB,EAAoBM,GAAGU,MAAOzc,GAASyb,EAAoBM,EAAE/b,GAAKic,EAASO,KAC9IP,EAASxT,OAAO+T,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbzd,EAAS2J,OAAO6T,IAAK,GACrB,IAAII,EAAIR,SACEhb,IAANwb,IAAiBV,EAASU,EAC/B,CACD,CACA,OAAOV,CAnBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIxd,EAASmC,OAAQqb,EAAI,GAAKxd,EAASwd,EAAI,GAAG,GAAKH,EAAUG,IAAKxd,EAASwd,GAAKxd,EAASwd,EAAI,GACrGxd,EAASwd,GAAK,CAACL,EAAUC,EAAIC,IkBJ/BV,EAAoBvR,EAAKjL,IACxB,IAAI0d,EAAS1d,GAAUA,EAAO2d,WAC7B,IAAO3d,EAAiB,QACxB,IAAM,EAEP,OADAwc,EAAoBoB,EAAEF,EAAQ,CAAE9D,EAAG8D,IAC5BA,GCLRlB,EAAoBoB,EAAI,CAACjB,EAASkB,KACjC,IAAI,IAAI9c,KAAO8c,EACXrB,EAAoBsB,EAAED,EAAY9c,KAASyb,EAAoBsB,EAAEnB,EAAS5b,IAC5Ewa,OAAOwC,eAAepB,EAAS5b,EAAK,CAAEid,YAAY,EAAMxb,IAAKqb,EAAW9c,MCJ3Eyb,EAAoBsB,EAAI,CAACG,EAAKC,IAAU3C,OAAO4C,UAAUC,eAAeC,KAAKJ,EAAKC,G,MCKlF,IAAII,EAAkB,CACrB,IAAK,GAaN9B,EAAoBM,EAAES,EAAKgB,GAA0C,IAA7BD,EAAgBC,GAGxD,IAAIC,EAAuB,CAACC,EAA4B/c,KACvD,IAGI+a,EAAU8B,GAHTvB,EAAU0B,EAAaC,GAAWjd,EAGhB2b,EAAI,EAC3B,GAAGL,EAAS4B,KAAM3e,GAAgC,IAAxBqe,EAAgBre,IAAa,CACtD,IAAIwc,KAAYiC,EACZlC,EAAoBsB,EAAEY,EAAajC,KACrCD,EAAoBK,EAAEJ,GAAYiC,EAAYjC,IAGhD,GAAGkC,EAAS,IAAI5B,EAAS4B,EAAQnC,EAClC,CAEA,IADGiC,GAA4BA,EAA2B/c,GACrD2b,EAAIL,EAAShb,OAAQqb,IACzBkB,EAAUvB,EAASK,GAChBb,EAAoBsB,EAAEQ,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAO/B,EAAoBM,EAAEC,IAG1B8B,EAAqBC,KAAiC,2BAAIA,KAAiC,4BAAK,GACpGD,EAAmBnV,QAAQ8U,EAAqBO,KAAK,KAAM,IAC3DF,EAAmB9e,KAAOye,EAAqBO,KAAK,KAAMF,EAAmB9e,KAAKgf,KAAKF,G,KChDvFrC,EAAoBwC,QAAK/c,ECGzB,IAAIgd,EAAsBzC,EAAoBM,OAAE7a,EAAW,CAAC,IAAK,IAAOua,EAAoB,MAC5FyC,EAAsBzC,EAAoBM,EAAEmC,E", "sources": ["webpack://ggcasecatalogs/webpack/runtime/chunk loaded", "webpack://ggcasecatalogs/./src/styles/global.css", "webpack://ggcasecatalogs/./src/styles/global.css?f0d8", "webpack://ggcasecatalogs/./src/lib/ApiService.js", "webpack://ggcasecatalogs/./src/lib/AuthService.js", "webpack://ggcasecatalogs/./src/components/NotificationService.js", "webpack://ggcasecatalogs/./src/login/LoginPage.js", "webpack://ggcasecatalogs/./src/admin/AdminDashboard.js", "webpack://ggcasecatalogs/./src/admin/pages/DashboardPage.js", "webpack://ggcasecatalogs/./src/admin/pages/ProductsPage.js", "webpack://ggcasecatalogs/./src/admin/pages/BrandsPage.js", "webpack://ggcasecatalogs/./src/admin/pages/CategoriesPage.js", "webpack://ggcasecatalogs/./src/admin/pages/BannersPage.js", "webpack://ggcasecatalogs/./src/admin/pages/AdminManagementPage.js", "webpack://ggcasecatalogs/./src/route/Router.js", "webpack://ggcasecatalogs/./src/App.js", "webpack://ggcasecatalogs/./src/index.js", "webpack://ggcasecatalogs/webpack/bootstrap", "webpack://ggcasecatalogs/webpack/runtime/compat get default export", "webpack://ggcasecatalogs/webpack/runtime/define property getters", "webpack://ggcasecatalogs/webpack/runtime/hasOwnProperty shorthand", "webpack://ggcasecatalogs/webpack/runtime/jsonp chunk loading", "webpack://ggcasecatalogs/webpack/runtime/nonce", "webpack://ggcasecatalogs/webpack/startup"], "sourcesContent": ["var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `/* Reset and base styles */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nhtml, body {\r\n  height: 100%;\r\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  color: #000000;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n#root {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* Typography */\r\nh1, h2, h3, h4, h5, h6 {\r\n  font-weight: 600;\r\n  line-height: 1.25;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\nh1 { font-size: 2rem; }\r\nh2 { font-size: 1.5rem; }\r\nh3 { font-size: 1.25rem; }\r\nh4 { font-size: 1.125rem; }\r\nh5 { font-size: 1rem; }\r\nh6 { font-size: 0.875rem; }\r\n\r\np {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Links */\r\na {\r\n  color: #E6B120;\r\n  text-decoration: none;\r\n  transition: color 0.2s;\r\n}\r\n\r\na:hover {\r\n  color: #FFCD29;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Buttons */\r\n.btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0.5rem 1rem;\r\n  border: 1px solid transparent;\r\n  border-radius: 0.375rem;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #E6B120;\r\n  color: #FFFFFF;\r\n  border-color: #E6B120;\r\n}\r\n\r\n.btn-primary:hover:not(:disabled) {\r\n  background-color: #FFCD29;\r\n  border-color: #FFCD29;\r\n  text-decoration: none;\r\n  color: #000000;\r\n}\r\n\r\n.btn-secondary {\r\n  background-color: #000000;\r\n  color: #FFFFFF;\r\n  border-color: #000000;\r\n}\r\n\r\n.btn-secondary:hover:not(:disabled) {\r\n  background-color: #333333;\r\n  border-color: #333333;\r\n  text-decoration: none;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.btn-danger {\r\n  background-color: #ef4444;\r\n  color: white;\r\n  border-color: #ef4444;\r\n}\r\n\r\n.btn-danger:hover:not(:disabled) {\r\n  background-color: #dc2626;\r\n  border-color: #dc2626;\r\n  text-decoration: none;\r\n  color: white;\r\n}\r\n\r\n.btn-outline {\r\n  background-color: transparent;\r\n  color: #000000;\r\n  border-color: #E6B120;\r\n}\r\n\r\n.btn-outline:hover:not(:disabled) {\r\n  background-color: #FFCD29;\r\n  text-decoration: none;\r\n  color: #000000;\r\n}\r\n\r\n.btn-sm {\r\n  padding: 0.25rem 0.75rem;\r\n  font-size: 0.75rem;\r\n}\r\n\r\n.btn-lg {\r\n  padding: 0.75rem 1.5rem;\r\n  font-size: 1rem;\r\n}\r\n\r\n/* Forms */\r\n.form-group {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-weight: 500;\r\n  margin-bottom: 0.25rem;\r\n  color: #000000;\r\n}\r\n\r\n.form-control {\r\n  display: block;\r\n  width: 100%;\r\n  padding: 0.5rem 0.75rem;\r\n  border: 1px solid #E6B120;\r\n  border-radius: 0.375rem;\r\n  font-size: 0.875rem;\r\n  line-height: 1.5;\r\n  color: #000000;\r\n  background-color: #FFFFFF;\r\n  transition: border-color 0.2s, box-shadow 0.2s;\r\n}\r\n\r\n.form-control:focus {\r\n  outline: none;\r\n  border-color: #FFCD29;\r\n  box-shadow: 0 0 0 3px rgba(255, 205, 41, 0.2);\r\n}\r\n\r\n.form-control:disabled {\r\n  background-color: #f9fafb;\r\n  color: #6b7280;\r\n}\r\n\r\n.form-control.is-invalid {\r\n  border-color: #ef4444;\r\n}\r\n\r\n.form-control.is-invalid:focus {\r\n  border-color: #ef4444;\r\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\r\n}\r\n\r\n.invalid-feedback {\r\n  display: block;\r\n  width: 100%;\r\n  margin-top: 0.25rem;\r\n  font-size: 0.75rem;\r\n  color: #ef4444;\r\n}\r\n\r\n/* Utilities */\r\n.text-center { text-align: center; }\r\n.text-left { text-align: left; }\r\n.text-right { text-align: right; }\r\n\r\n.d-none { display: none !important; }\r\n.d-block { display: block !important; }\r\n.d-flex { display: flex !important; }\r\n.d-inline-flex { display: inline-flex !important; }\r\n\r\n.justify-content-center { justify-content: center; }\r\n.justify-content-between { justify-content: space-between; }\r\n.justify-content-end { justify-content: flex-end; }\r\n\r\n.align-items-center { align-items: center; }\r\n.align-items-start { align-items: flex-start; }\r\n.align-items-end { align-items: flex-end; }\r\n\r\n.flex-column { flex-direction: column; }\r\n.flex-wrap { flex-wrap: wrap; }\r\n.flex-1 { flex: 1; }\r\n\r\n.gap-1 { gap: 0.25rem; }\r\n.gap-2 { gap: 0.5rem; }\r\n.gap-3 { gap: 0.75rem; }\r\n.gap-4 { gap: 1rem; }`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/styles/global.css\"],\"names\":[],\"mappings\":\"AAAA,0BAA0B;AAC1B;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,0HAA0H;EAC1H,eAAe;EACf,gBAAgB;EAChB,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,sBAAsB;AACxB;;AAEA,eAAe;AACf;EACE,gBAAgB;EAChB,iBAAiB;EACjB,qBAAqB;AACvB;;AAEA,KAAK,eAAe,EAAE;AACtB,KAAK,iBAAiB,EAAE;AACxB,KAAK,kBAAkB,EAAE;AACzB,KAAK,mBAAmB,EAAE;AAC1B,KAAK,eAAe,EAAE;AACtB,KAAK,mBAAmB,EAAE;;AAE1B;EACE,mBAAmB;AACrB;;AAEA,UAAU;AACV;EACE,cAAc;EACd,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA;EACE,cAAc;EACd,0BAA0B;AAC5B;;AAEA,YAAY;AACZ;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;EACpB,6BAA6B;EAC7B,uBAAuB;EACvB,mBAAmB;EACnB,gBAAgB;EAChB,qBAAqB;EACrB,eAAe;EACf,oBAAoB;EACpB,WAAW;AACb;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,yBAAyB;EACzB,cAAc;EACd,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,qBAAqB;EACrB,cAAc;AAChB;;AAEA;EACE,yBAAyB;EACzB,cAAc;EACd,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,qBAAqB;EACrB,cAAc;AAChB;;AAEA;EACE,yBAAyB;EACzB,YAAY;EACZ,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,qBAAqB;EACrB,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,cAAc;EACd,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,cAAc;AAChB;;AAEA;EACE,wBAAwB;EACxB,kBAAkB;AACpB;;AAEA;EACE,uBAAuB;EACvB,eAAe;AACjB;;AAEA,UAAU;AACV;EACE,mBAAmB;AACrB;;AAEA;EACE,cAAc;EACd,gBAAgB;EAChB,sBAAsB;EACtB,cAAc;AAChB;;AAEA;EACE,cAAc;EACd,WAAW;EACX,uBAAuB;EACvB,yBAAyB;EACzB,uBAAuB;EACvB,mBAAmB;EACnB,gBAAgB;EAChB,cAAc;EACd,yBAAyB;EACzB,8CAA8C;AAChD;;AAEA;EACE,aAAa;EACb,qBAAqB;EACrB,6CAA6C;AAC/C;;AAEA;EACE,yBAAyB;EACzB,cAAc;AAChB;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,qBAAqB;EACrB,4CAA4C;AAC9C;;AAEA;EACE,cAAc;EACd,WAAW;EACX,mBAAmB;EACnB,kBAAkB;EAClB,cAAc;AAChB;;AAEA,cAAc;AACd,eAAe,kBAAkB,EAAE;AACnC,aAAa,gBAAgB,EAAE;AAC/B,cAAc,iBAAiB,EAAE;;AAEjC,UAAU,wBAAwB,EAAE;AACpC,WAAW,yBAAyB,EAAE;AACtC,UAAU,wBAAwB,EAAE;AACpC,iBAAiB,+BAA+B,EAAE;;AAElD,0BAA0B,uBAAuB,EAAE;AACnD,2BAA2B,8BAA8B,EAAE;AAC3D,uBAAuB,yBAAyB,EAAE;;AAElD,sBAAsB,mBAAmB,EAAE;AAC3C,qBAAqB,uBAAuB,EAAE;AAC9C,mBAAmB,qBAAqB,EAAE;;AAE1C,eAAe,sBAAsB,EAAE;AACvC,aAAa,eAAe,EAAE;AAC9B,UAAU,OAAO,EAAE;;AAEnB,SAAS,YAAY,EAAE;AACvB,SAAS,WAAW,EAAE;AACtB,SAAS,YAAY,EAAE;AACvB,SAAS,SAAS,EAAE\",\"sourcesContent\":[\"/* Reset and base styles */\\r\\n* {\\r\\n  margin: 0;\\r\\n  padding: 0;\\r\\n  box-sizing: border-box;\\r\\n}\\r\\n\\r\\nhtml, body {\\r\\n  height: 100%;\\r\\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\\r\\n  font-size: 14px;\\r\\n  line-height: 1.5;\\r\\n  color: #000000;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n#root {\\r\\n  height: 100%;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n}\\r\\n\\r\\n/* Typography */\\r\\nh1, h2, h3, h4, h5, h6 {\\r\\n  font-weight: 600;\\r\\n  line-height: 1.25;\\r\\n  margin-bottom: 0.5rem;\\r\\n}\\r\\n\\r\\nh1 { font-size: 2rem; }\\r\\nh2 { font-size: 1.5rem; }\\r\\nh3 { font-size: 1.25rem; }\\r\\nh4 { font-size: 1.125rem; }\\r\\nh5 { font-size: 1rem; }\\r\\nh6 { font-size: 0.875rem; }\\r\\n\\r\\np {\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\n/* Links */\\r\\na {\\r\\n  color: #E6B120;\\r\\n  text-decoration: none;\\r\\n  transition: color 0.2s;\\r\\n}\\r\\n\\r\\na:hover {\\r\\n  color: #FFCD29;\\r\\n  text-decoration: underline;\\r\\n}\\r\\n\\r\\n/* Buttons */\\r\\n.btn {\\r\\n  display: inline-flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 0.5rem 1rem;\\r\\n  border: 1px solid transparent;\\r\\n  border-radius: 0.375rem;\\r\\n  font-size: 0.875rem;\\r\\n  font-weight: 500;\\r\\n  text-decoration: none;\\r\\n  cursor: pointer;\\r\\n  transition: all 0.2s;\\r\\n  gap: 0.5rem;\\r\\n}\\r\\n\\r\\n.btn:disabled {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.btn-primary {\\r\\n  background-color: #E6B120;\\r\\n  color: #FFFFFF;\\r\\n  border-color: #E6B120;\\r\\n}\\r\\n\\r\\n.btn-primary:hover:not(:disabled) {\\r\\n  background-color: #FFCD29;\\r\\n  border-color: #FFCD29;\\r\\n  text-decoration: none;\\r\\n  color: #000000;\\r\\n}\\r\\n\\r\\n.btn-secondary {\\r\\n  background-color: #000000;\\r\\n  color: #FFFFFF;\\r\\n  border-color: #000000;\\r\\n}\\r\\n\\r\\n.btn-secondary:hover:not(:disabled) {\\r\\n  background-color: #333333;\\r\\n  border-color: #333333;\\r\\n  text-decoration: none;\\r\\n  color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.btn-danger {\\r\\n  background-color: #ef4444;\\r\\n  color: white;\\r\\n  border-color: #ef4444;\\r\\n}\\r\\n\\r\\n.btn-danger:hover:not(:disabled) {\\r\\n  background-color: #dc2626;\\r\\n  border-color: #dc2626;\\r\\n  text-decoration: none;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.btn-outline {\\r\\n  background-color: transparent;\\r\\n  color: #000000;\\r\\n  border-color: #E6B120;\\r\\n}\\r\\n\\r\\n.btn-outline:hover:not(:disabled) {\\r\\n  background-color: #FFCD29;\\r\\n  text-decoration: none;\\r\\n  color: #000000;\\r\\n}\\r\\n\\r\\n.btn-sm {\\r\\n  padding: 0.25rem 0.75rem;\\r\\n  font-size: 0.75rem;\\r\\n}\\r\\n\\r\\n.btn-lg {\\r\\n  padding: 0.75rem 1.5rem;\\r\\n  font-size: 1rem;\\r\\n}\\r\\n\\r\\n/* Forms */\\r\\n.form-group {\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\n.form-label {\\r\\n  display: block;\\r\\n  font-weight: 500;\\r\\n  margin-bottom: 0.25rem;\\r\\n  color: #000000;\\r\\n}\\r\\n\\r\\n.form-control {\\r\\n  display: block;\\r\\n  width: 100%;\\r\\n  padding: 0.5rem 0.75rem;\\r\\n  border: 1px solid #E6B120;\\r\\n  border-radius: 0.375rem;\\r\\n  font-size: 0.875rem;\\r\\n  line-height: 1.5;\\r\\n  color: #000000;\\r\\n  background-color: #FFFFFF;\\r\\n  transition: border-color 0.2s, box-shadow 0.2s;\\r\\n}\\r\\n\\r\\n.form-control:focus {\\r\\n  outline: none;\\r\\n  border-color: #FFCD29;\\r\\n  box-shadow: 0 0 0 3px rgba(255, 205, 41, 0.2);\\r\\n}\\r\\n\\r\\n.form-control:disabled {\\r\\n  background-color: #f9fafb;\\r\\n  color: #6b7280;\\r\\n}\\r\\n\\r\\n.form-control.is-invalid {\\r\\n  border-color: #ef4444;\\r\\n}\\r\\n\\r\\n.form-control.is-invalid:focus {\\r\\n  border-color: #ef4444;\\r\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\r\\n}\\r\\n\\r\\n.invalid-feedback {\\r\\n  display: block;\\r\\n  width: 100%;\\r\\n  margin-top: 0.25rem;\\r\\n  font-size: 0.75rem;\\r\\n  color: #ef4444;\\r\\n}\\r\\n\\r\\n/* Utilities */\\r\\n.text-center { text-align: center; }\\r\\n.text-left { text-align: left; }\\r\\n.text-right { text-align: right; }\\r\\n\\r\\n.d-none { display: none !important; }\\r\\n.d-block { display: block !important; }\\r\\n.d-flex { display: flex !important; }\\r\\n.d-inline-flex { display: inline-flex !important; }\\r\\n\\r\\n.justify-content-center { justify-content: center; }\\r\\n.justify-content-between { justify-content: space-between; }\\r\\n.justify-content-end { justify-content: flex-end; }\\r\\n\\r\\n.align-items-center { align-items: center; }\\r\\n.align-items-start { align-items: flex-start; }\\r\\n.align-items-end { align-items: flex-end; }\\r\\n\\r\\n.flex-column { flex-direction: column; }\\r\\n.flex-wrap { flex-wrap: wrap; }\\r\\n.flex-1 { flex: 1; }\\r\\n\\r\\n.gap-1 { gap: 0.25rem; }\\r\\n.gap-2 { gap: 0.5rem; }\\r\\n.gap-3 { gap: 0.75rem; }\\r\\n.gap-4 { gap: 1rem; }\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "\n      import API from \"!../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../node_modules/css-loader/dist/cjs.js!./global.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../node_modules/css-loader/dist/cjs.js!./global.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "class ApiService {\n  constructor() {\n    this.baseURL = '/api';\n    this.defaultHeaders = {\n      'Content-Type': 'application/json',\n    };\n  }\n\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    \n    const config = {\n      headers: { ...this.defaultHeaders, ...options.headers },\n      credentials: 'include', // Include cookies for session management\n      ...options\n    };\n\n    try {\n      const response = await fetch(url, config);\n      \n      // Handle different content types\n      const contentType = response.headers.get('content-type');\n      let data;\n      \n      if (contentType && contentType.includes('application/json')) {\n        data = await response.json();\n      } else {\n        data = await response.text();\n      }\n\n      if (!response.ok) {\n        // If it's JSON error response, use the message from the response\n        if (typeof data === 'object' && data.message) {\n          throw new Error(data.message);\n        } else {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n      }\n\n      return data;\n    } catch (error) {\n      console.error(`API request failed: ${endpoint}`, error);\n      throw error;\n    }\n  }\n\n  async get(endpoint, params = {}) {\n    const queryString = new URLSearchParams(params).toString();\n    const url = queryString ? `${endpoint}?${queryString}` : endpoint;\n    \n    return this.request(url, {\n      method: 'GET'\n    });\n  }\n\n  async post(endpoint, data = {}) {\n    return this.request(endpoint, {\n      method: 'POST',\n      body: JSON.stringify(data)\n    });\n  }\n\n  async put(endpoint, data = {}) {\n    return this.request(endpoint, {\n      method: 'PUT',\n      body: JSON.stringify(data)\n    });\n  }\n\n  async delete(endpoint) {\n    return this.request(endpoint, {\n      method: 'DELETE'\n    });\n  }\n\n  async uploadFile(endpoint, formData) {\n    return this.request(endpoint, {\n      method: 'POST',\n      body: formData,\n      headers: {} // Let browser set Content-Type for FormData\n    });\n  }\n\n  // Utility methods for common API patterns\n  async getAll(resource) {\n    return this.get(`/${resource}`);\n  }\n\n  async getById(resource, id) {\n    return this.get(`/${resource}/${id}`);\n  }\n\n  async create(resource, data) {\n    return this.post(`/${resource}`, data);\n  }\n\n  async update(resource, id, data) {\n    return this.put(`/${resource}/${id}`, data);\n  }\n\n  async remove(resource, id) {\n    return this.delete(`/${resource}/${id}`);\n  }\n}\n\nexport { ApiService };\n", "import { ApiService } from './ApiService.js';\n\nclass AuthService {\n  constructor() {\n    this.apiService = new ApiService();\n    this.isAuthenticated = false;\n    this.user = null;\n    this.authStateListeners = [];\n    this.loginAttempts = 0;\n    this.maxLoginAttempts = 5;\n    this.lockoutTime = 15 * 60 * 1000; // 15 minutes\n    this.lockoutUntil = null;\n  }\n\n  async checkAuth() {\n    try {\n      const response = await this.apiService.get('/admin/session');\n      if (response.success && response.data.authenticated) {\n        this.isAuthenticated = true;\n        this.user = response.data.user;\n        this.notifyAuthStateChange(true);\n        return true;\n      } else {\n        this.isAuthenticated = false;\n        this.user = null;\n        this.notifyAuthStateChange(false);\n        return false;\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      this.isAuthenticated = false;\n      this.user = null;\n      this.notifyAuthStateChange(false);\n      return false;\n    }\n  }\n\n  async login(username, password) {\n    // Check if account is locked\n    if (this.isAccountLocked()) {\n      const remainingTime = Math.ceil((this.lockoutUntil - Date.now()) / 1000 / 60);\n      throw new Error(`Account locked. Try again in ${remainingTime} minutes.`);\n    }\n\n    try {\n      const response = await this.apiService.post('/admin/login', {\n        username,\n        password\n      });\n\n      if (response.success) {\n        this.isAuthenticated = true;\n        this.user = response.data.user;\n        this.loginAttempts = 0; // Reset attempts on successful login\n        this.lockoutUntil = null;\n        this.notifyAuthStateChange(true);\n        return response;\n      } else {\n        this.handleFailedLogin();\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      this.handleFailedLogin();\n      throw error;\n    }\n  }\n\n  async logout() {\n    try {\n      await this.apiService.post('/admin/logout');\n    } catch (error) {\n      console.error('Logout request failed:', error);\n    } finally {\n      this.isAuthenticated = false;\n      this.user = null;\n      this.notifyAuthStateChange(false);\n    }\n  }\n\n  handleFailedLogin() {\n    this.loginAttempts++;\n    \n    if (this.loginAttempts >= this.maxLoginAttempts) {\n      this.lockoutUntil = Date.now() + this.lockoutTime;\n      localStorage.setItem('lockoutUntil', this.lockoutUntil.toString());\n    }\n  }\n\n  isAccountLocked() {\n    const storedLockout = localStorage.getItem('lockoutUntil');\n    if (storedLockout) {\n      this.lockoutUntil = parseInt(storedLockout);\n      if (Date.now() < this.lockoutUntil) {\n        return true;\n      } else {\n        // Lockout expired\n        localStorage.removeItem('lockoutUntil');\n        this.lockoutUntil = null;\n        this.loginAttempts = 0;\n      }\n    }\n    return false;\n  }\n\n  getRemainingAttempts() {\n    return Math.max(0, this.maxLoginAttempts - this.loginAttempts);\n  }\n\n  getLockoutTimeRemaining() {\n    if (!this.isAccountLocked()) return 0;\n    return Math.max(0, this.lockoutUntil - Date.now());\n  }\n\n  onAuthStateChange(callback) {\n    this.authStateListeners.push(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      const index = this.authStateListeners.indexOf(callback);\n      if (index > -1) {\n        this.authStateListeners.splice(index, 1);\n      }\n    };\n  }\n\n  notifyAuthStateChange(isAuthenticated) {\n    this.authStateListeners.forEach(callback => {\n      try {\n        callback(isAuthenticated, this.user);\n      } catch (error) {\n        console.error('Error in auth state listener:', error);\n      }\n    });\n  }\n\n  getUser() {\n    return this.user;\n  }\n\n  isLoggedIn() {\n    return this.isAuthenticated;\n  }\n}\n\nexport { AuthService };\n", "class NotificationService {\n  constructor() {\n    this.container = null;\n    this.notifications = [];\n    this.nextId = 1;\n  }\n\n  init() {\n    this.container = document.getElementById('notification-root');\n    if (!this.container) {\n      console.error('Notification root element not found');\n      return;\n    }\n    \n    // Add notification styles\n    this.addStyles();\n  }\n\n  addStyles() {\n    if (document.getElementById('notification-styles')) return;\n    \n    const style = document.createElement('style');\n    style.id = 'notification-styles';\n    style.textContent = `\n      .notification-container {\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        z-index: 9999;\n        max-width: 400px;\n        pointer-events: none;\n      }\n      \n      .notification {\n        background: white;\n        border-radius: 8px;\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n        margin-bottom: 12px;\n        padding: 16px;\n        border-left: 4px solid;\n        pointer-events: auto;\n        transform: translateX(100%);\n        opacity: 0;\n        transition: all 0.3s ease;\n        display: flex;\n        align-items: flex-start;\n        gap: 12px;\n        max-width: 100%;\n        word-wrap: break-word;\n      }\n      \n      .notification.show {\n        transform: translateX(0);\n        opacity: 1;\n      }\n      \n      .notification.success {\n        border-left-color: #10b981;\n      }\n      \n      .notification.error {\n        border-left-color: #ef4444;\n      }\n      \n      .notification.warning {\n        border-left-color: #f59e0b;\n      }\n      \n      .notification.info {\n        border-left-color: #3b82f6;\n      }\n      \n      .notification-icon {\n        flex-shrink: 0;\n        width: 20px;\n        height: 20px;\n        margin-top: 2px;\n      }\n      \n      .notification-content {\n        flex: 1;\n        min-width: 0;\n      }\n      \n      .notification-title {\n        font-weight: 600;\n        font-size: 14px;\n        margin-bottom: 4px;\n        color: #1f2937;\n      }\n      \n      .notification-message {\n        font-size: 13px;\n        color: #6b7280;\n        line-height: 1.4;\n      }\n      \n      .notification-close {\n        background: none;\n        border: none;\n        color: #9ca3af;\n        cursor: pointer;\n        padding: 0;\n        width: 20px;\n        height: 20px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 4px;\n        transition: all 0.2s;\n        flex-shrink: 0;\n      }\n      \n      .notification-close:hover {\n        background-color: #f3f4f6;\n        color: #6b7280;\n      }\n      \n      @media (max-width: 640px) {\n        .notification-container {\n          left: 20px;\n          right: 20px;\n          max-width: none;\n        }\n      }\n    `;\n    document.head.appendChild(style);\n  }\n\n  show(type, title, message, duration = 5000) {\n    const notification = {\n      id: this.nextId++,\n      type,\n      title,\n      message,\n      duration\n    };\n\n    this.notifications.push(notification);\n    this.render();\n\n    // Auto-remove after duration\n    if (duration > 0) {\n      setTimeout(() => {\n        this.remove(notification.id);\n      }, duration);\n    }\n\n    return notification.id;\n  }\n\n  success(title, message, duration) {\n    return this.show('success', title, message, duration);\n  }\n\n  error(title, message, duration) {\n    return this.show('error', title, message, duration);\n  }\n\n  warning(title, message, duration) {\n    return this.show('warning', title, message, duration);\n  }\n\n  info(title, message, duration) {\n    return this.show('info', title, message, duration);\n  }\n\n  remove(id) {\n    const index = this.notifications.findIndex(n => n.id === id);\n    if (index > -1) {\n      this.notifications.splice(index, 1);\n      this.render();\n    }\n  }\n\n  clear() {\n    this.notifications = [];\n    this.render();\n  }\n\n  render() {\n    if (!this.container) return;\n\n    // Create or get container\n    let notificationContainer = this.container.querySelector('.notification-container');\n    if (!notificationContainer) {\n      notificationContainer = document.createElement('div');\n      notificationContainer.className = 'notification-container';\n      this.container.appendChild(notificationContainer);\n    }\n\n    // Clear existing notifications\n    notificationContainer.innerHTML = '';\n\n    // Render notifications\n    this.notifications.forEach(notification => {\n      const element = this.createNotificationElement(notification);\n      notificationContainer.appendChild(element);\n      \n      // Trigger animation\n      setTimeout(() => {\n        element.classList.add('show');\n      }, 10);\n    });\n  }\n\n  createNotificationElement(notification) {\n    const element = document.createElement('div');\n    element.className = `notification ${notification.type}`;\n    element.dataset.id = notification.id;\n\n    const icon = this.getIcon(notification.type);\n    \n    element.innerHTML = `\n      <div class=\"notification-icon\">${icon}</div>\n      <div class=\"notification-content\">\n        <div class=\"notification-title\">${this.escapeHtml(notification.title)}</div>\n        <div class=\"notification-message\">${this.escapeHtml(notification.message)}</div>\n      </div>\n      <button class=\"notification-close\" type=\"button\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    `;\n\n    // Add close handler\n    const closeBtn = element.querySelector('.notification-close');\n    closeBtn.addEventListener('click', () => {\n      this.remove(notification.id);\n    });\n\n    return element;\n  }\n\n  getIcon(type) {\n    const icons = {\n      success: '<i class=\"fas fa-check-circle\" style=\"color: #10b981;\"></i>',\n      error: '<i class=\"fas fa-exclamation-circle\" style=\"color: #ef4444;\"></i>',\n      warning: '<i class=\"fas fa-exclamation-triangle\" style=\"color: #f59e0b;\"></i>',\n      info: '<i class=\"fas fa-info-circle\" style=\"color: #3b82f6;\"></i>'\n    };\n    return icons[type] || icons.info;\n  }\n\n  escapeHtml(text) {\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n}\n\nexport { NotificationService };\n", "import { AuthService } from '../lib/AuthService.js';\nimport { NotificationService } from '../components/NotificationService.js';\n\nclass LoginPage {\n  constructor() {\n    this.authService = new AuthService();\n    this.notificationService = new NotificationService();\n    this.showPassword = false;\n    this.isLoading = false;\n  }\n\n  async render(container) {\n    container.innerHTML = this.getHTML();\n    this.bindEvents();\n    this.updateLoginAttempts();\n  }\n\n  getHTML() {\n    return `\n      <div class=\"login-container\">\n        <div class=\"login-card\">\n          <div class=\"login-header\">\n            <h1>GG Case Catalogs</h1>\n            <h2>Admin Dashboard</h2>\n            <p>Sign in to manage your catalog</p>\n          </div>\n          \n          <form class=\"login-form\" id=\"loginForm\">\n            <div class=\"form-group\">\n              <label for=\"username\" class=\"form-label\">Username</label>\n              <input \n                type=\"text\" \n                id=\"username\" \n                name=\"username\" \n                class=\"form-control\" \n                required \n                autocomplete=\"username\"\n                placeholder=\"Enter your username\"\n              >\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"password\" class=\"form-label\">Password</label>\n              <div class=\"password-input-container\">\n                <input \n                  type=\"password\" \n                  id=\"password\" \n                  name=\"password\" \n                  class=\"form-control password-input\" \n                  required \n                  autocomplete=\"current-password\"\n                  placeholder=\"Enter your password\"\n                >\n                <button \n                  type=\"button\" \n                  class=\"password-toggle\" \n                  id=\"passwordToggle\"\n                  title=\"Toggle password visibility\"\n                >\n                  <i class=\"fas fa-eye\" id=\"passwordToggleIcon\"></i>\n                </button>\n              </div>\n            </div>\n            \n            <div class=\"login-attempts\" id=\"loginAttempts\"></div>\n            \n            <button \n              type=\"submit\" \n              class=\"btn btn-primary btn-lg login-submit\" \n              id=\"loginSubmit\"\n            >\n              <span class=\"submit-text\">Sign In</span>\n              <span class=\"submit-loading d-none\">\n                <i class=\"fas fa-spinner fa-spin\"></i>\n                Signing in...\n              </span>\n            </button>\n          </form>\n          \n          <div class=\"login-footer\">\n            <p class=\"text-center\">\n              <small>Secure admin access for authorized personnel only</small>\n            </p>\n          </div>\n        </div>\n      </div>\n      \n      <style>\n        .login-container {\n          min-height: 100vh;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background: linear-gradient(135deg, #E6B120 0%, #FFCD29 100%);\n          padding: 2rem;\n        }\n        \n        .login-card {\n          background: #FFFFFF;\n          border-radius: 12px;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);\n          padding: 3rem;\n          width: 100%;\n          max-width: 400px;\n          border: 2px solid #E6B120;\n        }\n        \n        .login-header {\n          text-align: center;\n          margin-bottom: 2rem;\n        }\n        \n        .login-header h1 {\n          color: #000000;\n          font-size: 1.75rem;\n          margin-bottom: 0.5rem;\n        }\n\n        .login-header h2 {\n          color: #E6B120;\n          font-size: 1.25rem;\n          font-weight: 500;\n          margin-bottom: 0.5rem;\n        }\n\n        .login-header p {\n          color: #000000;\n          font-size: 0.875rem;\n          margin-bottom: 0;\n          opacity: 0.7;\n        }\n        \n        .password-input-container {\n          position: relative;\n        }\n        \n        .password-input {\n          padding-right: 3rem;\n        }\n        \n        .password-toggle {\n          position: absolute;\n          right: 0.75rem;\n          top: 50%;\n          transform: translateY(-50%);\n          background: none;\n          border: none;\n          color: #E6B120;\n          cursor: pointer;\n          padding: 0.25rem;\n          border-radius: 0.25rem;\n          transition: color 0.2s;\n        }\n\n        .password-toggle:hover {\n          color: #FFCD29;\n        }\n        \n        .login-submit {\n          width: 100%;\n          margin-top: 1rem;\n        }\n        \n        .login-attempts {\n          margin-bottom: 1rem;\n          padding: 0.75rem;\n          border-radius: 0.375rem;\n          font-size: 0.875rem;\n          text-align: center;\n        }\n        \n        .login-attempts.warning {\n          background-color: #fef3c7;\n          color: #92400e;\n          border: 1px solid #fcd34d;\n        }\n        \n        .login-attempts.error {\n          background-color: #fee2e2;\n          color: #991b1b;\n          border: 1px solid #fca5a5;\n        }\n        \n        .login-attempts.hidden {\n          display: none;\n        }\n        \n        .login-footer {\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n        \n        @media (max-width: 480px) {\n          .login-container {\n            padding: 1rem;\n          }\n          \n          .login-card {\n            padding: 2rem;\n          }\n        }\n      </style>\n    `;\n  }\n\n  bindEvents() {\n    const form = document.getElementById('loginForm');\n    const passwordToggle = document.getElementById('passwordToggle');\n    const passwordInput = document.getElementById('password');\n    const passwordToggleIcon = document.getElementById('passwordToggleIcon');\n\n    // Handle form submission\n    form.addEventListener('submit', (e) => {\n      e.preventDefault();\n      this.handleLogin();\n    });\n\n    // Handle password visibility toggle\n    passwordToggle.addEventListener('click', () => {\n      this.showPassword = !this.showPassword;\n      passwordInput.type = this.showPassword ? 'text' : 'password';\n      passwordToggleIcon.className = this.showPassword ? 'fas fa-eye-slash' : 'fas fa-eye';\n    });\n\n    // Handle Enter key in password field\n    passwordInput.addEventListener('keypress', (e) => {\n      if (e.key === 'Enter') {\n        this.handleLogin();\n      }\n    });\n  }\n\n  async handleLogin() {\n    if (this.isLoading) return;\n\n    const form = document.getElementById('loginForm');\n    const submitBtn = document.getElementById('loginSubmit');\n    const submitText = submitBtn.querySelector('.submit-text');\n    const submitLoading = submitBtn.querySelector('.submit-loading');\n    const usernameInput = document.getElementById('username');\n    const passwordInput = document.getElementById('password');\n\n    const username = usernameInput.value.trim();\n    const password = passwordInput.value;\n\n    if (!username || !password) {\n      this.notificationService.error('Validation Error', 'Please enter both username and password');\n      return;\n    }\n\n    this.isLoading = true;\n    submitBtn.disabled = true;\n    submitText.classList.add('d-none');\n    submitLoading.classList.remove('d-none');\n\n    try {\n      await this.authService.login(username, password);\n      this.notificationService.success('Login Successful', 'Welcome to the admin dashboard');\n      \n      // Redirect will be handled by the router\n      setTimeout(() => {\n        window.location.href = '/admin/dashboard';\n      }, 1000);\n      \n    } catch (error) {\n      console.error('Login error:', error);\n      this.notificationService.error('Login Failed', error.message);\n      this.updateLoginAttempts();\n      \n      // Clear password field on failed login\n      passwordInput.value = '';\n      passwordInput.focus();\n      \n    } finally {\n      this.isLoading = false;\n      submitBtn.disabled = false;\n      submitText.classList.remove('d-none');\n      submitLoading.classList.add('d-none');\n    }\n  }\n\n  updateLoginAttempts() {\n    const attemptsContainer = document.getElementById('loginAttempts');\n    if (!attemptsContainer) return;\n\n    const remainingAttempts = this.authService.getRemainingAttempts();\n    const isLocked = this.authService.isAccountLocked();\n    const lockoutTime = this.authService.getLockoutTimeRemaining();\n\n    if (isLocked) {\n      const minutes = Math.ceil(lockoutTime / 1000 / 60);\n      attemptsContainer.innerHTML = `\n        <i class=\"fas fa-lock\"></i>\n        Account locked. Try again in ${minutes} minute${minutes !== 1 ? 's' : ''}.\n      `;\n      attemptsContainer.className = 'login-attempts error';\n    } else if (remainingAttempts < 5 && remainingAttempts > 0) {\n      attemptsContainer.innerHTML = `\n        <i class=\"fas fa-exclamation-triangle\"></i>\n        ${remainingAttempts} login attempt${remainingAttempts !== 1 ? 's' : ''} remaining\n      `;\n      attemptsContainer.className = 'login-attempts warning';\n    } else {\n      attemptsContainer.className = 'login-attempts hidden';\n    }\n  }\n}\n\nexport default LoginPage;\n", "import { AuthService } from '../lib/AuthService.js';\nimport { NotificationService } from '../components/NotificationService.js';\n\nclass AdminDashboard {\n  constructor() {\n    this.authService = new AuthService();\n    this.notificationService = new NotificationService();\n    this.sidebarCollapsed = false;\n  }\n\n  async render(container) {\n    container.innerHTML = this.getHTML();\n    this.bindEvents();\n    await this.loadUserInfo();\n  }\n\n  getHTML() {\n    return `\n      <div class=\"admin-layout\">\n        <aside class=\"admin-sidebar\" id=\"adminSidebar\">\n          <div class=\"sidebar-header\">\n            <div class=\"sidebar-logo\">\n              <i class=\"fas fa-cube\"></i>\n              <span class=\"sidebar-logo-text\">GG Catalogs</span>\n            </div>\n            <button class=\"sidebar-toggle\" id=\"sidebarToggle\">\n              <i class=\"fas fa-bars\"></i>\n            </button>\n          </div>\n          \n          <nav class=\"sidebar-nav\">\n            <ul class=\"nav-list\">\n              <li class=\"nav-item\">\n                <a href=\"/admin/dashboard\" class=\"nav-link\" data-route=\"/admin/dashboard\">\n                  <i class=\"fas fa-tachometer-alt\"></i>\n                  <span class=\"nav-text\">Dashboard</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a href=\"/admin/products\" class=\"nav-link\" data-route=\"/admin/products\">\n                  <i class=\"fas fa-box\"></i>\n                  <span class=\"nav-text\">Products</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a href=\"/admin/brands\" class=\"nav-link\" data-route=\"/admin/brands\">\n                  <i class=\"fas fa-tags\"></i>\n                  <span class=\"nav-text\">Brands</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a href=\"/admin/categories\" class=\"nav-link\" data-route=\"/admin/categories\">\n                  <i class=\"fas fa-list\"></i>\n                  <span class=\"nav-text\">Categories</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a href=\"/admin/banners\" class=\"nav-link\" data-route=\"/admin/banners\">\n                  <i class=\"fas fa-image\"></i>\n                  <span class=\"nav-text\">Banners</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a href=\"/admin/admins\" class=\"nav-link\" data-route=\"/admin/admins\">\n                  <i class=\"fas fa-users-cog\"></i>\n                  <span class=\"nav-text\">Admin Users</span>\n                </a>\n              </li>\n            </ul>\n          </nav>\n        </aside>\n        \n        <div class=\"admin-main\">\n          <header class=\"admin-header\">\n            <div class=\"header-left\">\n              <button class=\"mobile-menu-toggle\" id=\"mobileMenuToggle\">\n                <i class=\"fas fa-bars\"></i>\n              </button>\n              <h1 class=\"page-title\" id=\"pageTitle\">Dashboard</h1>\n            </div>\n            \n            <div class=\"header-right\">\n              <div class=\"user-menu\">\n                <button class=\"user-menu-toggle\" id=\"userMenuToggle\">\n                  <div class=\"user-avatar\">\n                    <i class=\"fas fa-user\"></i>\n                  </div>\n                  <span class=\"user-name\" id=\"userName\">Admin</span>\n                  <i class=\"fas fa-chevron-down\"></i>\n                </button>\n                \n                <div class=\"user-menu-dropdown\" id=\"userMenuDropdown\">\n                  <a href=\"#\" class=\"dropdown-item\" id=\"logoutBtn\">\n                    <i class=\"fas fa-sign-out-alt\"></i>\n                    Logout\n                  </a>\n                </div>\n              </div>\n            </div>\n          </header>\n          \n          <main class=\"admin-content\" id=\"adminContent\">\n            <!-- Page content will be loaded here -->\n          </main>\n        </div>\n      </div>\n      \n      <style>\n        .admin-layout {\n          display: flex;\n          height: 100vh;\n          background-color: #FFFFFF;\n        }\n\n        .admin-sidebar {\n          width: 260px;\n          background: #000000;\n          color: #FFFFFF;\n          transition: all 0.3s ease;\n          position: relative;\n          z-index: 1000;\n        }\n        \n        .admin-sidebar.collapsed {\n          width: 70px;\n        }\n        \n        .sidebar-header {\n          padding: 1rem;\n          border-bottom: 1px solid #E6B120;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n\n        .sidebar-logo {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          font-weight: 600;\n          font-size: 1.125rem;\n        }\n\n        .sidebar-logo i {\n          font-size: 1.5rem;\n          color: #FFCD29;\n        }\n        \n        .sidebar-toggle {\n          background: none;\n          border: none;\n          color: #E6B120;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .sidebar-toggle:hover {\n          background-color: #E6B120;\n          color: #000000;\n        }\n        \n        .sidebar-nav {\n          padding: 1rem 0;\n        }\n        \n        .nav-list {\n          list-style: none;\n          margin: 0;\n          padding: 0;\n        }\n        \n        .nav-item {\n          margin-bottom: 0.25rem;\n        }\n        \n        .nav-link {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          padding: 0.75rem 1rem;\n          color: #FFFFFF;\n          text-decoration: none;\n          transition: all 0.2s;\n          border-left: 3px solid transparent;\n        }\n\n        .nav-link:hover {\n          background-color: #E6B120;\n          color: #000000;\n          text-decoration: none;\n        }\n\n        .nav-link.active {\n          background-color: #FFCD29;\n          color: #000000;\n          border-left-color: #E6B120;\n        }\n        \n        .nav-link i {\n          width: 20px;\n          text-align: center;\n        }\n        \n        .admin-sidebar.collapsed .sidebar-logo-text,\n        .admin-sidebar.collapsed .nav-text {\n          display: none;\n        }\n        \n        .admin-main {\n          flex: 1;\n          display: flex;\n          flex-direction: column;\n          overflow: hidden;\n        }\n        \n        .admin-header {\n          background: #FFFFFF;\n          border-bottom: 1px solid #E6B120;\n          padding: 0 1.5rem;\n          height: 64px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          box-shadow: 0 1px 3px rgba(230, 177, 32, 0.2);\n        }\n        \n        .header-left {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n        }\n        \n        .mobile-menu-toggle {\n          display: none;\n          background: none;\n          border: none;\n          color: #64748b;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n        }\n        \n        .page-title {\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: #000000;\n          margin: 0;\n        }\n        \n        .user-menu {\n          position: relative;\n        }\n        \n        .user-menu-toggle {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          background: none;\n          border: none;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.5rem;\n          transition: background-color 0.2s;\n        }\n        \n        .user-menu-toggle:hover {\n          background-color: #f1f5f9;\n        }\n        \n        .user-avatar {\n          width: 32px;\n          height: 32px;\n          background: #E6B120;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #000000;\n        }\n\n        .user-name {\n          font-weight: 500;\n          color: #000000;\n        }\n        \n        .user-menu-dropdown {\n          position: absolute;\n          top: 100%;\n          right: 0;\n          background: white;\n          border: 1px solid #e2e8f0;\n          border-radius: 0.5rem;\n          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n          min-width: 150px;\n          z-index: 1000;\n          display: none;\n        }\n        \n        .user-menu-dropdown.show {\n          display: block;\n        }\n        \n        .dropdown-item {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 0.75rem 1rem;\n          color: #374151;\n          text-decoration: none;\n          transition: background-color 0.2s;\n        }\n        \n        .dropdown-item:hover {\n          background-color: #f9fafb;\n          text-decoration: none;\n          color: #374151;\n        }\n        \n        .admin-content {\n          flex: 1;\n          overflow-y: auto;\n          padding: 1.5rem;\n        }\n        \n        @media (max-width: 768px) {\n          .admin-sidebar {\n            position: fixed;\n            left: -260px;\n            height: 100vh;\n            z-index: 1001;\n          }\n          \n          .admin-sidebar.mobile-open {\n            left: 0;\n          }\n          \n          .admin-main {\n            width: 100%;\n          }\n          \n          .mobile-menu-toggle {\n            display: block;\n          }\n          \n          .admin-content {\n            padding: 1rem;\n          }\n        }\n      </style>\n    `;\n  }\n\n  bindEvents() {\n    const sidebarToggle = document.getElementById('sidebarToggle');\n    const mobileMenuToggle = document.getElementById('mobileMenuToggle');\n    const userMenuToggle = document.getElementById('userMenuToggle');\n    const userMenuDropdown = document.getElementById('userMenuDropdown');\n    const logoutBtn = document.getElementById('logoutBtn');\n    const sidebar = document.getElementById('adminSidebar');\n\n    // Sidebar toggle\n    sidebarToggle?.addEventListener('click', () => {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      sidebar.classList.toggle('collapsed', this.sidebarCollapsed);\n    });\n\n    // Mobile menu toggle\n    mobileMenuToggle?.addEventListener('click', () => {\n      sidebar.classList.toggle('mobile-open');\n    });\n\n    // User menu toggle\n    userMenuToggle?.addEventListener('click', (e) => {\n      e.stopPropagation();\n      userMenuDropdown.classList.toggle('show');\n    });\n\n    // Close user menu when clicking outside\n    document.addEventListener('click', () => {\n      userMenuDropdown.classList.remove('show');\n    });\n\n    // Logout\n    logoutBtn?.addEventListener('click', async (e) => {\n      e.preventDefault();\n      await this.handleLogout();\n    });\n\n    // Navigation links\n    const navLinks = document.querySelectorAll('.nav-link');\n    navLinks.forEach(link => {\n      link.addEventListener('click', (e) => {\n        e.preventDefault();\n        const route = link.dataset.route;\n        if (route) {\n          this.navigateToRoute(route);\n        }\n      });\n    });\n\n    // Update active nav link based on current route\n    this.updateActiveNavLink();\n  }\n\n  async loadUserInfo() {\n    try {\n      const user = this.authService.getUser();\n      if (user) {\n        const userNameElement = document.getElementById('userName');\n        if (userNameElement) {\n          userNameElement.textContent = user.username || 'Admin';\n        }\n      }\n    } catch (error) {\n      console.error('Error loading user info:', error);\n    }\n  }\n\n  async handleLogout() {\n    try {\n      await this.authService.logout();\n      this.notificationService.success('Logged Out', 'You have been successfully logged out');\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      this.notificationService.error('Logout Error', 'There was an error logging out');\n    }\n  }\n\n  navigateToRoute(route) {\n    // Update active nav link\n    const navLinks = document.querySelectorAll('.nav-link');\n    navLinks.forEach(link => {\n      link.classList.remove('active');\n      if (link.dataset.route === route) {\n        link.classList.add('active');\n      }\n    });\n\n    // Update page title\n    const pageTitle = document.getElementById('pageTitle');\n    if (pageTitle) {\n      const titles = {\n        '/admin/dashboard': 'Dashboard',\n        '/admin/products': 'Products',\n        '/admin/brands': 'Brands',\n        '/admin/categories': 'Categories',\n        '/admin/banners': 'Banners',\n        '/admin/admins': 'Admin Users'\n      };\n      pageTitle.textContent = titles[route] || 'Dashboard';\n    }\n\n    // Navigate using router\n    if (window.history) {\n      window.history.pushState({}, '', route);\n      window.dispatchEvent(new PopStateEvent('popstate'));\n    }\n  }\n\n  updateActiveNavLink() {\n    const currentPath = window.location.pathname;\n    const navLinks = document.querySelectorAll('.nav-link');\n    \n    navLinks.forEach(link => {\n      link.classList.remove('active');\n      if (link.dataset.route === currentPath) {\n        link.classList.add('active');\n      }\n    });\n  }\n}\n\nexport default AdminDashboard;\n", "import { ApiService } from '../../lib/ApiService.js';\nimport { NotificationService } from '../../components/NotificationService.js';\n\nclass DashboardPage {\n  constructor() {\n    this.apiService = new ApiService();\n    this.notificationService = new NotificationService();\n    this.stats = {\n      products: 0,\n      brands: 0,\n      categories: 0,\n      banners: 0\n    };\n  }\n\n  async render(container) {\n    container.innerHTML = this.getHTML();\n    await this.loadStats();\n  }\n\n  getHTML() {\n    return `\n      <div class=\"dashboard-page\">\n        <div class=\"dashboard-header\">\n          <h1>Dashboard Overview</h1>\n          <p>Welcome to the GG Case Catalogs admin dashboard</p>\n        </div>\n        \n        <div class=\"stats-grid\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon products\">\n              <i class=\"fas fa-box\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3 id=\"productsCount\">-</h3>\n              <p>Total Products</p>\n            </div>\n          </div>\n          \n          <div class=\"stat-card\">\n            <div class=\"stat-icon brands\">\n              <i class=\"fas fa-tags\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3 id=\"brandsCount\">-</h3>\n              <p>Total Brands</p>\n            </div>\n          </div>\n          \n          <div class=\"stat-card\">\n            <div class=\"stat-icon categories\">\n              <i class=\"fas fa-list\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3 id=\"categoriesCount\">-</h3>\n              <p>Total Categories</p>\n            </div>\n          </div>\n          \n          <div class=\"stat-card\">\n            <div class=\"stat-icon banners\">\n              <i class=\"fas fa-image\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3 id=\"bannersCount\">-</h3>\n              <p>Active Banners</p>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"dashboard-content\">\n          <div class=\"dashboard-row\">\n            <div class=\"dashboard-col\">\n              <div class=\"card\">\n                <div class=\"card-header\">\n                  <h3>Quick Actions</h3>\n                </div>\n                <div class=\"card-body\">\n                  <div class=\"quick-actions\">\n                    <a href=\"/admin/products\" class=\"quick-action-btn\">\n                      <i class=\"fas fa-plus\"></i>\n                      Add Product\n                    </a>\n                    <a href=\"/admin/brands\" class=\"quick-action-btn\">\n                      <i class=\"fas fa-plus\"></i>\n                      Add Brand\n                    </a>\n                    <a href=\"/admin/categories\" class=\"quick-action-btn\">\n                      <i class=\"fas fa-plus\"></i>\n                      Add Category\n                    </a>\n                    <a href=\"/admin/banners\" class=\"quick-action-btn\">\n                      <i class=\"fas fa-plus\"></i>\n                      Add Banner\n                    </a>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"dashboard-col\">\n              <div class=\"card\">\n                <div class=\"card-header\">\n                  <h3>System Status</h3>\n                </div>\n                <div class=\"card-body\">\n                  <div class=\"status-item\">\n                    <span class=\"status-label\">Database</span>\n                    <span class=\"status-indicator online\" id=\"dbStatus\">\n                      <i class=\"fas fa-circle\"></i>\n                      Online\n                    </span>\n                  </div>\n                  <div class=\"status-item\">\n                    <span class=\"status-label\">API Server</span>\n                    <span class=\"status-indicator online\">\n                      <i class=\"fas fa-circle\"></i>\n                      Running\n                    </span>\n                  </div>\n                  <div class=\"status-item\">\n                    <span class=\"status-label\">File Uploads</span>\n                    <span class=\"status-indicator online\">\n                      <i class=\"fas fa-circle\"></i>\n                      Available\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <style>\n        .dashboard-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n        \n        .dashboard-header {\n          margin-bottom: 2rem;\n        }\n        \n        .dashboard-header h1 {\n          font-size: 2rem;\n          color: #000000;\n          margin-bottom: 0.5rem;\n        }\n\n        .dashboard-header p {\n          color: #000000;\n          font-size: 1rem;\n          opacity: 0.7;\n        }\n        \n        .stats-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 1.5rem;\n          margin-bottom: 2rem;\n        }\n        \n        .stat-card {\n          background: #FFFFFF;\n          border-radius: 0.75rem;\n          padding: 1.5rem;\n          box-shadow: 0 1px 3px rgba(230, 177, 32, 0.2);\n          border: 1px solid #E6B120;\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .stat-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(255, 205, 41, 0.3);\n        }\n        \n        .stat-icon {\n          width: 60px;\n          height: 60px;\n          border-radius: 0.75rem;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 1.5rem;\n          color: white;\n        }\n        \n        .stat-icon.products { background: linear-gradient(135deg, #E6B120 0%, #FFCD29 100%); }\n        .stat-icon.brands { background: linear-gradient(135deg, #000000 0%, #333333 100%); }\n        .stat-icon.categories { background: linear-gradient(135deg, #FFCD29 0%, #E6B120 100%); }\n        .stat-icon.banners { background: linear-gradient(135deg, #000000 0%, #E6B120 100%); }\n        \n        .stat-content h3 {\n          font-size: 2rem;\n          font-weight: 700;\n          color: #000000;\n          margin-bottom: 0.25rem;\n        }\n\n        .stat-content p {\n          color: #000000;\n          font-size: 0.875rem;\n          margin: 0;\n          opacity: 0.7;\n        }\n        \n        .dashboard-content {\n          margin-top: 2rem;\n        }\n        \n        .dashboard-row {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n          gap: 1.5rem;\n        }\n        \n        .card {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n        }\n        \n        .card-header {\n          padding: 1.5rem 1.5rem 0;\n        }\n        \n        .card-header h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin: 0;\n        }\n        \n        .card-body {\n          padding: 1.5rem;\n        }\n        \n        .quick-actions {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n          gap: 1rem;\n        }\n        \n        .quick-action-btn {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 1rem;\n          background: #f8fafc;\n          border: 1px solid #e2e8f0;\n          border-radius: 0.5rem;\n          text-decoration: none;\n          color: #475569;\n          transition: all 0.2s;\n        }\n        \n        .quick-action-btn:hover {\n          background: #3b82f6;\n          color: white;\n          text-decoration: none;\n          transform: translateY(-1px);\n        }\n        \n        .quick-action-btn i {\n          font-size: 1.25rem;\n        }\n        \n        .status-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 0.75rem 0;\n          border-bottom: 1px solid #f1f5f9;\n        }\n        \n        .status-item:last-child {\n          border-bottom: none;\n        }\n        \n        .status-label {\n          font-weight: 500;\n          color: #374151;\n        }\n        \n        .status-indicator {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n        \n        .status-indicator.online {\n          color: #059669;\n        }\n        \n        .status-indicator.offline {\n          color: #dc2626;\n        }\n        \n        .status-indicator i {\n          font-size: 0.5rem;\n        }\n        \n        @media (max-width: 768px) {\n          .stats-grid {\n            grid-template-columns: 1fr;\n          }\n          \n          .dashboard-row {\n            grid-template-columns: 1fr;\n          }\n          \n          .quick-actions {\n            grid-template-columns: repeat(2, 1fr);\n          }\n        }\n      </style>\n    `;\n  }\n\n  async loadStats() {\n    try {\n      // Load all stats in parallel\n      const [products, brands, categories, banners] = await Promise.all([\n        this.apiService.get('/products').catch(() => ({ data: [] })),\n        this.apiService.get('/brands').catch(() => ({ data: [] })),\n        this.apiService.get('/categories').catch(() => ({ data: [] })),\n        this.apiService.get('/banners/active').catch(() => ({ data: [] }))\n      ]);\n\n      // Update stats\n      this.stats.products = products.data?.length || 0;\n      this.stats.brands = brands.data?.length || 0;\n      this.stats.categories = categories.data?.length || 0;\n      this.stats.banners = banners.data?.length || 0;\n\n      // Update UI\n      this.updateStatsDisplay();\n\n      // Test database connection\n      await this.testDatabaseConnection();\n\n    } catch (error) {\n      console.error('Error loading dashboard stats:', error);\n      this.notificationService.error('Error', 'Failed to load dashboard statistics');\n    }\n  }\n\n  updateStatsDisplay() {\n    const elements = {\n      productsCount: document.getElementById('productsCount'),\n      brandsCount: document.getElementById('brandsCount'),\n      categoriesCount: document.getElementById('categoriesCount'),\n      bannersCount: document.getElementById('bannersCount')\n    };\n\n    if (elements.productsCount) elements.productsCount.textContent = this.stats.products;\n    if (elements.brandsCount) elements.brandsCount.textContent = this.stats.brands;\n    if (elements.categoriesCount) elements.categoriesCount.textContent = this.stats.categories;\n    if (elements.bannersCount) elements.bannersCount.textContent = this.stats.banners;\n  }\n\n  async testDatabaseConnection() {\n    try {\n      await this.apiService.get('/test-db');\n      const dbStatus = document.getElementById('dbStatus');\n      if (dbStatus) {\n        dbStatus.className = 'status-indicator online';\n        dbStatus.innerHTML = '<i class=\"fas fa-circle\"></i> Online';\n      }\n    } catch (error) {\n      const dbStatus = document.getElementById('dbStatus');\n      if (dbStatus) {\n        dbStatus.className = 'status-indicator offline';\n        dbStatus.innerHTML = '<i class=\"fas fa-circle\"></i> Offline';\n      }\n    }\n  }\n}\n\nexport default DashboardPage;\n", "import { ApiService } from '../../lib/ApiService.js';\nimport { NotificationService } from '../../components/NotificationService.js';\n\nclass ProductsPage {\n  constructor() {\n    this.apiService = new ApiService();\n    this.notificationService = new NotificationService();\n    this.products = [];\n    this.brands = [];\n    this.categories = [];\n    this.showForm = false;\n    this.editingProduct = null;\n  }\n\n  async render(container) {\n    container.innerHTML = this.getHTML();\n    this.bindEvents();\n    await this.loadData();\n  }\n\n  getHTML() {\n    return `\n      <div class=\"products-page\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Products Management</h1>\n            <p>Manage your product catalog</p>\n          </div>\n          <button class=\"btn btn-primary\" id=\"addProductBtn\">\n            <i class=\"fas fa-plus\"></i>\n            Add Product\n          </button>\n        </div>\n\n        <div class=\"products-content\">\n          <div class=\"products-list\" id=\"productsList\">\n            <div class=\"loading-state\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Loading products...\n            </div>\n          </div>\n\n          <div class=\"product-form-modal\" id=\"productFormModal\" style=\"display: none;\">\n            <div class=\"modal-backdrop\"></div>\n            <div class=\"modal-content\">\n              <div class=\"modal-header\">\n                <h3 id=\"formTitle\">Add New Product</h3>\n                <button class=\"modal-close\" id=\"closeFormBtn\">\n                  <i class=\"fas fa-times\"></i>\n                </button>\n              </div>\n              <form class=\"product-form\" id=\"productForm\">\n                <div class=\"form-row\">\n                  <div class=\"form-group\">\n                    <label for=\"productName\" class=\"form-label\">Product Name *</label>\n                    <input type=\"text\" id=\"productName\" name=\"name\" class=\"form-control\" required>\n                  </div>\n                </div>\n                \n                <div class=\"form-group\">\n                  <label for=\"productDescription\" class=\"form-label\">Description</label>\n                  <textarea id=\"productDescription\" name=\"description\" class=\"form-control\" rows=\"4\"></textarea>\n                </div>\n                \n                <div class=\"form-row\">\n                  <div class=\"form-group\">\n                    <label for=\"productBrand\" class=\"form-label\">Brand</label>\n                    <select id=\"productBrand\" name=\"brand_id\" class=\"form-control\">\n                      <option value=\"\">Select Brand</option>\n                    </select>\n                  </div>\n                  <div class=\"form-group\">\n                    <label for=\"productCategory\" class=\"form-label\">Category</label>\n                    <select id=\"productCategory\" name=\"category_id\" class=\"form-control\">\n                      <option value=\"\">Select Category</option>\n                    </select>\n                  </div>\n                </div>\n\n                <div class=\"form-actions\">\n                  <button type=\"button\" class=\"btn btn-secondary\" id=\"cancelFormBtn\">Cancel</button>\n                  <button type=\"submit\" class=\"btn btn-primary\" id=\"saveProductBtn\">\n                    <span class=\"btn-text\">Save Product</span>\n                    <span class=\"btn-loading d-none\">\n                      <i class=\"fas fa-spinner fa-spin\"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .products-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .products-list {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n        }\n\n        .loading-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .products-table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        .products-table th,\n        .products-table td {\n          padding: 1rem;\n          text-align: left;\n          border-bottom: 1px solid #f1f5f9;\n        }\n\n        .products-table th {\n          background: #f8fafc;\n          font-weight: 600;\n          color: #374151;\n        }\n\n        .products-table tr:hover {\n          background: #f9fafb;\n        }\n\n        .product-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-edit:hover {\n          background: #2563eb;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .product-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 600px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .product-form {\n          padding: 1.5rem;\n        }\n\n        .form-row {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 1rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .form-row {\n            grid-template-columns: 1fr;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n\n          .products-table {\n            font-size: 0.875rem;\n          }\n\n          .products-table th,\n          .products-table td {\n            padding: 0.75rem 0.5rem;\n          }\n        }\n      </style>\n    `;\n  }\n\n  bindEvents() {\n    const addProductBtn = document.getElementById('addProductBtn');\n    const closeFormBtn = document.getElementById('closeFormBtn');\n    const cancelFormBtn = document.getElementById('cancelFormBtn');\n    const productForm = document.getElementById('productForm');\n    const modal = document.getElementById('productFormModal');\n    const modalBackdrop = modal?.querySelector('.modal-backdrop');\n\n    addProductBtn?.addEventListener('click', () => this.showProductForm());\n    closeFormBtn?.addEventListener('click', () => this.hideProductForm());\n    cancelFormBtn?.addEventListener('click', () => this.hideProductForm());\n    modalBackdrop?.addEventListener('click', () => this.hideProductForm());\n    productForm?.addEventListener('submit', (e) => this.handleFormSubmit(e));\n  }\n\n  async loadData() {\n    try {\n      const [productsResponse, brandsResponse, categoriesResponse] = await Promise.all([\n        this.apiService.get('/products'),\n        this.apiService.get('/brands'),\n        this.apiService.get('/categories')\n      ]);\n\n      this.products = productsResponse.data || [];\n      this.brands = brandsResponse.data || [];\n      this.categories = categoriesResponse.data || [];\n\n      this.renderProductsList();\n      this.populateFormSelects();\n\n    } catch (error) {\n      console.error('Error loading products data:', error);\n      this.notificationService.error('Error', 'Failed to load products data');\n      this.renderError();\n    }\n  }\n\n  renderProductsList() {\n    const container = document.getElementById('productsList');\n    if (!container) return;\n\n    if (this.products.length === 0) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-box\"></i>\n          <h3>No Products Found</h3>\n          <p>Start by adding your first product to the catalog.</p>\n        </div>\n      `;\n      return;\n    }\n\n    const tableHTML = `\n      <table class=\"products-table\">\n        <thead>\n          <tr>\n            <th>Name</th>\n            <th>Brand</th>\n            <th>Category</th>\n            <th>Created</th>\n            <th>Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${this.products.map(product => `\n            <tr>\n              <td>\n                <div>\n                  <div class=\"font-weight-500\">${this.escapeHtml(product.name)}</div>\n                  ${product.description ? `<div class=\"text-sm text-gray-500\">${this.escapeHtml(product.description.substring(0, 60))}${product.description.length > 60 ? '...' : ''}</div>` : ''}\n                </div>\n              </td>\n              <td>${product.brand_name || '-'}</td>\n              <td>${product.category_name || '-'}</td>\n              <td>${new Date(product.created_at).toLocaleDateString()}</td>\n              <td>\n                <div class=\"product-actions\">\n                  <button class=\"btn-icon btn-edit\" onclick=\"window.productsPage.editProduct(${product.id})\" title=\"Edit\">\n                    <i class=\"fas fa-edit\"></i>\n                  </button>\n                  <button class=\"btn-icon btn-delete\" onclick=\"window.productsPage.deleteProduct(${product.id})\" title=\"Delete\">\n                    <i class=\"fas fa-trash\"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n    `;\n\n    container.innerHTML = tableHTML;\n    \n    // Make this instance globally accessible for onclick handlers\n    window.productsPage = this;\n  }\n\n  renderError() {\n    const container = document.getElementById('productsList');\n    if (container) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <h3>Error Loading Products</h3>\n          <p>There was an error loading the products. Please try again.</p>\n          <button class=\"btn btn-primary\" onclick=\"window.location.reload()\">Retry</button>\n        </div>\n      `;\n    }\n  }\n\n  populateFormSelects() {\n    const brandSelect = document.getElementById('productBrand');\n    const categorySelect = document.getElementById('productCategory');\n\n    if (brandSelect) {\n      brandSelect.innerHTML = '<option value=\"\">Select Brand</option>' +\n        this.brands.map(brand => `<option value=\"${brand.id}\">${this.escapeHtml(brand.name)}</option>`).join('');\n    }\n\n    if (categorySelect) {\n      categorySelect.innerHTML = '<option value=\"\">Select Category</option>' +\n        this.categories.map(category => `<option value=\"${category.id}\">${this.escapeHtml(category.name)}</option>`).join('');\n    }\n  }\n\n  showProductForm(product = null) {\n    this.editingProduct = product;\n    const modal = document.getElementById('productFormModal');\n    const form = document.getElementById('productForm');\n    const title = document.getElementById('formTitle');\n\n    if (product) {\n      title.textContent = 'Edit Product';\n      this.populateForm(product);\n    } else {\n      title.textContent = 'Add New Product';\n      form.reset();\n    }\n\n    modal.style.display = 'flex';\n    document.body.style.overflow = 'hidden';\n  }\n\n  hideProductForm() {\n    const modal = document.getElementById('productFormModal');\n    modal.style.display = 'none';\n    document.body.style.overflow = '';\n    this.editingProduct = null;\n  }\n\n  populateForm(product) {\n    document.getElementById('productName').value = product.name || '';\n    document.getElementById('productDescription').value = product.description || '';\n    document.getElementById('productBrand').value = product.brand_id || '';\n    document.getElementById('productCategory').value = product.category_id || '';\n  }\n\n  async handleFormSubmit(e) {\n    e.preventDefault();\n    \n    const saveBtn = document.getElementById('saveProductBtn');\n    const btnText = saveBtn.querySelector('.btn-text');\n    const btnLoading = saveBtn.querySelector('.btn-loading');\n    \n    const formData = new FormData(e.target);\n    const productData = {\n      name: formData.get('name'),\n      description: formData.get('description'),\n      brand_id: formData.get('brand_id') || null,\n      category_id: formData.get('category_id') || null\n    };\n\n    saveBtn.disabled = true;\n    btnText.classList.add('d-none');\n    btnLoading.classList.remove('d-none');\n\n    try {\n      if (this.editingProduct) {\n        await this.apiService.put(`/products/${this.editingProduct.id}`, productData);\n        this.notificationService.success('Success', 'Product updated successfully');\n      } else {\n        await this.apiService.post('/products', productData);\n        this.notificationService.success('Success', 'Product created successfully');\n      }\n\n      this.hideProductForm();\n      await this.loadData();\n\n    } catch (error) {\n      console.error('Error saving product:', error);\n      this.notificationService.error('Error', error.message || 'Failed to save product');\n    } finally {\n      saveBtn.disabled = false;\n      btnText.classList.remove('d-none');\n      btnLoading.classList.add('d-none');\n    }\n  }\n\n  editProduct(id) {\n    const product = this.products.find(p => p.id === id);\n    if (product) {\n      this.showProductForm(product);\n    }\n  }\n\n  async deleteProduct(id) {\n    const product = this.products.find(p => p.id === id);\n    if (!product) return;\n\n    if (!confirm(`Are you sure you want to delete \"${product.name}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      await this.apiService.delete(`/products/${id}`);\n      this.notificationService.success('Success', 'Product deleted successfully');\n      await this.loadData();\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      this.notificationService.error('Error', error.message || 'Failed to delete product');\n    }\n  }\n\n  escapeHtml(text) {\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n}\n\nexport default ProductsPage;\n", "import { ApiService } from '../../lib/ApiService.js';\nimport { NotificationService } from '../../components/NotificationService.js';\n\nclass BrandsPage {\n  constructor() {\n    this.apiService = new ApiService();\n    this.notificationService = new NotificationService();\n    this.brands = [];\n    this.showForm = false;\n    this.editingBrand = null;\n  }\n\n  async render(container) {\n    container.innerHTML = this.getHTML();\n    this.bindEvents();\n    await this.loadBrands();\n  }\n\n  getHTML() {\n    return `\n      <div class=\"brands-page\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Brands Management</h1>\n            <p>Manage product brands and their information</p>\n          </div>\n          <button class=\"btn btn-primary\" id=\"addBrandBtn\">\n            <i class=\"fas fa-plus\"></i>\n            Add Brand\n          </button>\n        </div>\n\n        <div class=\"brands-content\">\n          <div class=\"brands-grid\" id=\"brandsGrid\">\n            <div class=\"loading-state\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Loading brands...\n            </div>\n          </div>\n\n          <div class=\"brand-form-modal\" id=\"brandFormModal\" style=\"display: none;\">\n            <div class=\"modal-backdrop\"></div>\n            <div class=\"modal-content\">\n              <div class=\"modal-header\">\n                <h3 id=\"formTitle\">Add New Brand</h3>\n                <button class=\"modal-close\" id=\"closeFormBtn\">\n                  <i class=\"fas fa-times\"></i>\n                </button>\n              </div>\n              <form class=\"brand-form\" id=\"brandForm\">\n                <div class=\"form-group\">\n                  <label for=\"brandName\" class=\"form-label\">Brand Name *</label>\n                  <input type=\"text\" id=\"brandName\" name=\"name\" class=\"form-control\" required>\n                </div>\n                \n                <div class=\"form-group\">\n                  <label for=\"brandPhoto\" class=\"form-label\">Brand Photo URL</label>\n                  <input type=\"url\" id=\"brandPhoto\" name=\"brand_photo\" class=\"form-control\" placeholder=\"https://example.com/logo.jpg\">\n                  <small class=\"form-text\">Enter a URL for the brand logo/photo</small>\n                </div>\n\n                <div class=\"form-actions\">\n                  <button type=\"button\" class=\"btn btn-secondary\" id=\"cancelFormBtn\">Cancel</button>\n                  <button type=\"submit\" class=\"btn btn-primary\" id=\"saveBrandBtn\">\n                    <span class=\"btn-text\">Save Brand</span>\n                    <span class=\"btn-loading d-none\">\n                      <i class=\"fas fa-spinner fa-spin\"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .brands-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .brands-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n          gap: 1.5rem;\n        }\n\n        .loading-state {\n          grid-column: 1 / -1;\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .brand-card {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .brand-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .brand-image {\n          height: 150px;\n          background: #f8fafc;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-bottom: 1px solid #e2e8f0;\n          overflow: hidden;\n        }\n\n        .brand-image img {\n          max-width: 100%;\n          max-height: 100%;\n          object-fit: contain;\n        }\n\n        .brand-image .placeholder {\n          color: #94a3b8;\n          font-size: 3rem;\n        }\n\n        .brand-content {\n          padding: 1.5rem;\n        }\n\n        .brand-name {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .brand-meta {\n          color: #64748b;\n          font-size: 0.875rem;\n          margin-bottom: 1rem;\n        }\n\n        .brand-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          flex: 1;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-edit:hover {\n          background: #2563eb;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .brand-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 500px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .brand-form {\n          padding: 1.5rem;\n        }\n\n        .form-text {\n          color: #6b7280;\n          font-size: 0.75rem;\n          margin-top: 0.25rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          grid-column: 1 / -1;\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .brands-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n        }\n      </style>\n    `;\n  }\n\n  bindEvents() {\n    const addBrandBtn = document.getElementById('addBrandBtn');\n    const closeFormBtn = document.getElementById('closeFormBtn');\n    const cancelFormBtn = document.getElementById('cancelFormBtn');\n    const brandForm = document.getElementById('brandForm');\n    const modal = document.getElementById('brandFormModal');\n    const modalBackdrop = modal?.querySelector('.modal-backdrop');\n\n    addBrandBtn?.addEventListener('click', () => this.showBrandForm());\n    closeFormBtn?.addEventListener('click', () => this.hideBrandForm());\n    cancelFormBtn?.addEventListener('click', () => this.hideBrandForm());\n    modalBackdrop?.addEventListener('click', () => this.hideBrandForm());\n    brandForm?.addEventListener('submit', (e) => this.handleFormSubmit(e));\n  }\n\n  async loadBrands() {\n    try {\n      const response = await this.apiService.get('/brands');\n      this.brands = response.data || [];\n      this.renderBrandsGrid();\n    } catch (error) {\n      console.error('Error loading brands:', error);\n      this.notificationService.error('Error', 'Failed to load brands');\n      this.renderError();\n    }\n  }\n\n  renderBrandsGrid() {\n    const container = document.getElementById('brandsGrid');\n    if (!container) return;\n\n    if (this.brands.length === 0) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-tags\"></i>\n          <h3>No Brands Found</h3>\n          <p>Start by adding your first brand to organize your products.</p>\n        </div>\n      `;\n      return;\n    }\n\n    const cardsHTML = this.brands.map(brand => `\n      <div class=\"brand-card\">\n        <div class=\"brand-image\">\n          ${brand.brand_photo ? \n            `<img src=\"${brand.brand_photo}\" alt=\"${this.escapeHtml(brand.name)}\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">\n             <div class=\"placeholder\" style=\"display: none;\"><i class=\"fas fa-image\"></i></div>` :\n            `<div class=\"placeholder\"><i class=\"fas fa-image\"></i></div>`\n          }\n        </div>\n        <div class=\"brand-content\">\n          <h3 class=\"brand-name\">${this.escapeHtml(brand.name)}</h3>\n          <div class=\"brand-meta\">\n            Created: ${new Date(brand.created_at).toLocaleDateString()}\n          </div>\n          <div class=\"brand-actions\">\n            <button class=\"btn-icon btn-edit\" onclick=\"window.brandsPage.editBrand(${brand.id})\">\n              <i class=\"fas fa-edit\"></i>\n              Edit\n            </button>\n            <button class=\"btn-icon btn-delete\" onclick=\"window.brandsPage.deleteBrand(${brand.id})\">\n              <i class=\"fas fa-trash\"></i>\n              Delete\n            </button>\n          </div>\n        </div>\n      </div>\n    `).join('');\n\n    container.innerHTML = cardsHTML;\n    \n    // Make this instance globally accessible for onclick handlers\n    window.brandsPage = this;\n  }\n\n  renderError() {\n    const container = document.getElementById('brandsGrid');\n    if (container) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <h3>Error Loading Brands</h3>\n          <p>There was an error loading the brands. Please try again.</p>\n          <button class=\"btn btn-primary\" onclick=\"window.location.reload()\">Retry</button>\n        </div>\n      `;\n    }\n  }\n\n  showBrandForm(brand = null) {\n    this.editingBrand = brand;\n    const modal = document.getElementById('brandFormModal');\n    const form = document.getElementById('brandForm');\n    const title = document.getElementById('formTitle');\n\n    if (brand) {\n      title.textContent = 'Edit Brand';\n      this.populateForm(brand);\n    } else {\n      title.textContent = 'Add New Brand';\n      form.reset();\n    }\n\n    modal.style.display = 'flex';\n    document.body.style.overflow = 'hidden';\n  }\n\n  hideBrandForm() {\n    const modal = document.getElementById('brandFormModal');\n    modal.style.display = 'none';\n    document.body.style.overflow = '';\n    this.editingBrand = null;\n  }\n\n  populateForm(brand) {\n    document.getElementById('brandName').value = brand.name || '';\n    document.getElementById('brandPhoto').value = brand.brand_photo || '';\n  }\n\n  async handleFormSubmit(e) {\n    e.preventDefault();\n    \n    const saveBtn = document.getElementById('saveBrandBtn');\n    const btnText = saveBtn.querySelector('.btn-text');\n    const btnLoading = saveBtn.querySelector('.btn-loading');\n    \n    const formData = new FormData(e.target);\n    const brandData = {\n      name: formData.get('name'),\n      brand_photo: formData.get('brand_photo') || null\n    };\n\n    saveBtn.disabled = true;\n    btnText.classList.add('d-none');\n    btnLoading.classList.remove('d-none');\n\n    try {\n      if (this.editingBrand) {\n        await this.apiService.put(`/brands/${this.editingBrand.id}`, brandData);\n        this.notificationService.success('Success', 'Brand updated successfully');\n      } else {\n        await this.apiService.post('/brands', brandData);\n        this.notificationService.success('Success', 'Brand created successfully');\n      }\n\n      this.hideBrandForm();\n      await this.loadBrands();\n\n    } catch (error) {\n      console.error('Error saving brand:', error);\n      this.notificationService.error('Error', error.message || 'Failed to save brand');\n    } finally {\n      saveBtn.disabled = false;\n      btnText.classList.remove('d-none');\n      btnLoading.classList.add('d-none');\n    }\n  }\n\n  editBrand(id) {\n    const brand = this.brands.find(b => b.id === id);\n    if (brand) {\n      this.showBrandForm(brand);\n    }\n  }\n\n  async deleteBrand(id) {\n    const brand = this.brands.find(b => b.id === id);\n    if (!brand) return;\n\n    if (!confirm(`Are you sure you want to delete \"${brand.name}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      await this.apiService.delete(`/brands/${id}`);\n      this.notificationService.success('Success', 'Brand deleted successfully');\n      await this.loadBrands();\n    } catch (error) {\n      console.error('Error deleting brand:', error);\n      this.notificationService.error('Error', error.message || 'Failed to delete brand');\n    }\n  }\n\n  escapeHtml(text) {\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n}\n\nexport default BrandsPage;\n", "import { ApiService } from '../../lib/ApiService.js';\nimport { NotificationService } from '../../components/NotificationService.js';\n\nclass CategoriesPage {\n  constructor() {\n    this.apiService = new ApiService();\n    this.notificationService = new NotificationService();\n    this.categories = [];\n    this.showForm = false;\n    this.editingCategory = null;\n  }\n\n  async render(container) {\n    container.innerHTML = this.getHTML();\n    this.bindEvents();\n    await this.loadCategories();\n  }\n\n  getHTML() {\n    return `\n      <div class=\"categories-page\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Categories Management</h1>\n            <p>Organize your products with categories</p>\n          </div>\n          <button class=\"btn btn-primary\" id=\"addCategoryBtn\">\n            <i class=\"fas fa-plus\"></i>\n            Add Category\n          </button>\n        </div>\n\n        <div class=\"categories-content\">\n          <div class=\"categories-grid\" id=\"categoriesGrid\">\n            <div class=\"loading-state\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Loading categories...\n            </div>\n          </div>\n\n          <div class=\"category-form-modal\" id=\"categoryFormModal\" style=\"display: none;\">\n            <div class=\"modal-backdrop\"></div>\n            <div class=\"modal-content\">\n              <div class=\"modal-header\">\n                <h3 id=\"formTitle\">Add New Category</h3>\n                <button class=\"modal-close\" id=\"closeFormBtn\">\n                  <i class=\"fas fa-times\"></i>\n                </button>\n              </div>\n              <form class=\"category-form\" id=\"categoryForm\">\n                <div class=\"form-group\">\n                  <label for=\"categoryName\" class=\"form-label\">Category Name *</label>\n                  <input type=\"text\" id=\"categoryName\" name=\"name\" class=\"form-control\" required>\n                </div>\n                \n                <div class=\"form-group\">\n                  <label for=\"categoryPhoto\" class=\"form-label\">Category Photo URL</label>\n                  <input type=\"url\" id=\"categoryPhoto\" name=\"category_photo\" class=\"form-control\" placeholder=\"https://example.com/category.jpg\">\n                  <small class=\"form-text\">Enter a URL for the category image</small>\n                </div>\n\n                <div class=\"form-actions\">\n                  <button type=\"button\" class=\"btn btn-secondary\" id=\"cancelFormBtn\">Cancel</button>\n                  <button type=\"submit\" class=\"btn btn-primary\" id=\"saveCategoryBtn\">\n                    <span class=\"btn-text\">Save Category</span>\n                    <span class=\"btn-loading d-none\">\n                      <i class=\"fas fa-spinner fa-spin\"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .categories-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .categories-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n          gap: 1.5rem;\n        }\n\n        .loading-state {\n          grid-column: 1 / -1;\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .category-card {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .category-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .category-image {\n          height: 150px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-bottom: 1px solid #e2e8f0;\n          overflow: hidden;\n          position: relative;\n        }\n\n        .category-image img {\n          max-width: 100%;\n          max-height: 100%;\n          object-fit: cover;\n          width: 100%;\n          height: 100%;\n        }\n\n        .category-image .placeholder {\n          color: white;\n          font-size: 3rem;\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n        }\n\n        .category-content {\n          padding: 1.5rem;\n        }\n\n        .category-name {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .category-meta {\n          color: #64748b;\n          font-size: 0.875rem;\n          margin-bottom: 1rem;\n        }\n\n        .category-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          flex: 1;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-edit:hover {\n          background: #2563eb;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .category-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 500px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .category-form {\n          padding: 1.5rem;\n        }\n\n        .form-text {\n          color: #6b7280;\n          font-size: 0.75rem;\n          margin-top: 0.25rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          grid-column: 1 / -1;\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .categories-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n        }\n      </style>\n    `;\n  }\n\n  bindEvents() {\n    const addCategoryBtn = document.getElementById('addCategoryBtn');\n    const closeFormBtn = document.getElementById('closeFormBtn');\n    const cancelFormBtn = document.getElementById('cancelFormBtn');\n    const categoryForm = document.getElementById('categoryForm');\n    const modal = document.getElementById('categoryFormModal');\n    const modalBackdrop = modal?.querySelector('.modal-backdrop');\n\n    addCategoryBtn?.addEventListener('click', () => this.showCategoryForm());\n    closeFormBtn?.addEventListener('click', () => this.hideCategoryForm());\n    cancelFormBtn?.addEventListener('click', () => this.hideCategoryForm());\n    modalBackdrop?.addEventListener('click', () => this.hideCategoryForm());\n    categoryForm?.addEventListener('submit', (e) => this.handleFormSubmit(e));\n  }\n\n  async loadCategories() {\n    try {\n      const response = await this.apiService.get('/categories');\n      this.categories = response.data || [];\n      this.renderCategoriesGrid();\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      this.notificationService.error('Error', 'Failed to load categories');\n      this.renderError();\n    }\n  }\n\n  renderCategoriesGrid() {\n    const container = document.getElementById('categoriesGrid');\n    if (!container) return;\n\n    if (this.categories.length === 0) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-list\"></i>\n          <h3>No Categories Found</h3>\n          <p>Start by adding your first category to organize your products.</p>\n        </div>\n      `;\n      return;\n    }\n\n    const cardsHTML = this.categories.map(category => `\n      <div class=\"category-card\">\n        <div class=\"category-image\">\n          ${category.category_photo ? \n            `<img src=\"${category.category_photo}\" alt=\"${this.escapeHtml(category.name)}\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">\n             <div class=\"placeholder\" style=\"display: none;\"><i class=\"fas fa-folder\"></i></div>` :\n            `<div class=\"placeholder\"><i class=\"fas fa-folder\"></i></div>`\n          }\n        </div>\n        <div class=\"category-content\">\n          <h3 class=\"category-name\">${this.escapeHtml(category.name)}</h3>\n          <div class=\"category-meta\">\n            Created: ${new Date(category.created_at).toLocaleDateString()}\n          </div>\n          <div class=\"category-actions\">\n            <button class=\"btn-icon btn-edit\" onclick=\"window.categoriesPage.editCategory(${category.id})\">\n              <i class=\"fas fa-edit\"></i>\n              Edit\n            </button>\n            <button class=\"btn-icon btn-delete\" onclick=\"window.categoriesPage.deleteCategory(${category.id})\">\n              <i class=\"fas fa-trash\"></i>\n              Delete\n            </button>\n          </div>\n        </div>\n      </div>\n    `).join('');\n\n    container.innerHTML = cardsHTML;\n    \n    // Make this instance globally accessible for onclick handlers\n    window.categoriesPage = this;\n  }\n\n  renderError() {\n    const container = document.getElementById('categoriesGrid');\n    if (container) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <h3>Error Loading Categories</h3>\n          <p>There was an error loading the categories. Please try again.</p>\n          <button class=\"btn btn-primary\" onclick=\"window.location.reload()\">Retry</button>\n        </div>\n      `;\n    }\n  }\n\n  showCategoryForm(category = null) {\n    this.editingCategory = category;\n    const modal = document.getElementById('categoryFormModal');\n    const form = document.getElementById('categoryForm');\n    const title = document.getElementById('formTitle');\n\n    if (category) {\n      title.textContent = 'Edit Category';\n      this.populateForm(category);\n    } else {\n      title.textContent = 'Add New Category';\n      form.reset();\n    }\n\n    modal.style.display = 'flex';\n    document.body.style.overflow = 'hidden';\n  }\n\n  hideCategoryForm() {\n    const modal = document.getElementById('categoryFormModal');\n    modal.style.display = 'none';\n    document.body.style.overflow = '';\n    this.editingCategory = null;\n  }\n\n  populateForm(category) {\n    document.getElementById('categoryName').value = category.name || '';\n    document.getElementById('categoryPhoto').value = category.category_photo || '';\n  }\n\n  async handleFormSubmit(e) {\n    e.preventDefault();\n    \n    const saveBtn = document.getElementById('saveCategoryBtn');\n    const btnText = saveBtn.querySelector('.btn-text');\n    const btnLoading = saveBtn.querySelector('.btn-loading');\n    \n    const formData = new FormData(e.target);\n    const categoryData = {\n      name: formData.get('name'),\n      category_photo: formData.get('category_photo') || null\n    };\n\n    saveBtn.disabled = true;\n    btnText.classList.add('d-none');\n    btnLoading.classList.remove('d-none');\n\n    try {\n      if (this.editingCategory) {\n        await this.apiService.put(`/categories/${this.editingCategory.id}`, categoryData);\n        this.notificationService.success('Success', 'Category updated successfully');\n      } else {\n        await this.apiService.post('/categories', categoryData);\n        this.notificationService.success('Success', 'Category created successfully');\n      }\n\n      this.hideCategoryForm();\n      await this.loadCategories();\n\n    } catch (error) {\n      console.error('Error saving category:', error);\n      this.notificationService.error('Error', error.message || 'Failed to save category');\n    } finally {\n      saveBtn.disabled = false;\n      btnText.classList.remove('d-none');\n      btnLoading.classList.add('d-none');\n    }\n  }\n\n  editCategory(id) {\n    const category = this.categories.find(c => c.id === id);\n    if (category) {\n      this.showCategoryForm(category);\n    }\n  }\n\n  async deleteCategory(id) {\n    const category = this.categories.find(c => c.id === id);\n    if (!category) return;\n\n    if (!confirm(`Are you sure you want to delete \"${category.name}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      await this.apiService.delete(`/categories/${id}`);\n      this.notificationService.success('Success', 'Category deleted successfully');\n      await this.loadCategories();\n    } catch (error) {\n      console.error('Error deleting category:', error);\n      this.notificationService.error('Error', error.message || 'Failed to delete category');\n    }\n  }\n\n  escapeHtml(text) {\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n}\n\nexport default CategoriesPage;\n", "import { ApiService } from '../../lib/ApiService.js';\nimport { NotificationService } from '../../components/NotificationService.js';\n\nclass BannersPage {\n  constructor() {\n    this.apiService = new ApiService();\n    this.notificationService = new NotificationService();\n    this.banners = [];\n    this.showForm = false;\n    this.editingBanner = null;\n  }\n\n  async render(container) {\n    container.innerHTML = this.getHTML();\n    this.bindEvents();\n    await this.loadBanners();\n  }\n\n  getHTML() {\n    return `\n      <div class=\"banners-page\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Banners Management</h1>\n            <p>Manage website banners and promotional content</p>\n          </div>\n          <button class=\"btn btn-primary\" id=\"addBannerBtn\">\n            <i class=\"fas fa-plus\"></i>\n            Add Banner\n          </button>\n        </div>\n\n        <div class=\"banners-content\">\n          <div class=\"banners-list\" id=\"bannersList\">\n            <div class=\"loading-state\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Loading banners...\n            </div>\n          </div>\n\n          <div class=\"banner-form-modal\" id=\"bannerFormModal\" style=\"display: none;\">\n            <div class=\"modal-backdrop\"></div>\n            <div class=\"modal-content\">\n              <div class=\"modal-header\">\n                <h3 id=\"formTitle\">Add New Banner</h3>\n                <button class=\"modal-close\" id=\"closeFormBtn\">\n                  <i class=\"fas fa-times\"></i>\n                </button>\n              </div>\n              <form class=\"banner-form\" id=\"bannerForm\">\n                <div class=\"form-group\">\n                  <label for=\"bannerTitle\" class=\"form-label\">Banner Title *</label>\n                  <input type=\"text\" id=\"bannerTitle\" name=\"title\" class=\"form-control\" required>\n                </div>\n                \n                <div class=\"form-group\">\n                  <label for=\"bannerImage\" class=\"form-label\">Banner Image URL *</label>\n                  <input type=\"url\" id=\"bannerImage\" name=\"banner_image_url\" class=\"form-control\" required placeholder=\"https://example.com/banner.jpg\">\n                  <small class=\"form-text\">Enter a URL for the banner image</small>\n                </div>\n                \n                <div class=\"form-group\">\n                  <label for=\"bannerRedirect\" class=\"form-label\">Redirect URL</label>\n                  <input type=\"url\" id=\"bannerRedirect\" name=\"redirect_url\" class=\"form-control\" placeholder=\"https://example.com/page\">\n                  <small class=\"form-text\">URL to redirect when banner is clicked (optional)</small>\n                </div>\n                \n                <div class=\"form-group\">\n                  <label class=\"form-label\">\n                    <input type=\"checkbox\" id=\"bannerActive\" name=\"active\" checked>\n                    Active Banner\n                  </label>\n                  <small class=\"form-text\">Only active banners will be displayed on the website</small>\n                </div>\n\n                <div class=\"form-actions\">\n                  <button type=\"button\" class=\"btn btn-secondary\" id=\"cancelFormBtn\">Cancel</button>\n                  <button type=\"submit\" class=\"btn btn-primary\" id=\"saveBannerBtn\">\n                    <span class=\"btn-text\">Save Banner</span>\n                    <span class=\"btn-loading d-none\">\n                      <i class=\"fas fa-spinner fa-spin\"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .banners-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .banners-list {\n          display: grid;\n          gap: 1.5rem;\n        }\n\n        .loading-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .banner-card {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .banner-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .banner-preview {\n          height: 200px;\n          background: #f8fafc;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          overflow: hidden;\n          position: relative;\n        }\n\n        .banner-preview img {\n          max-width: 100%;\n          max-height: 100%;\n          object-fit: cover;\n          width: 100%;\n          height: 100%;\n        }\n\n        .banner-preview .placeholder {\n          color: #94a3b8;\n          font-size: 3rem;\n        }\n\n        .banner-status {\n          position: absolute;\n          top: 1rem;\n          right: 1rem;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .banner-status.active {\n          background: #dcfce7;\n          color: #166534;\n        }\n\n        .banner-status.inactive {\n          background: #fee2e2;\n          color: #991b1b;\n        }\n\n        .banner-content {\n          padding: 1.5rem;\n        }\n\n        .banner-title {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .banner-meta {\n          color: #64748b;\n          font-size: 0.875rem;\n          margin-bottom: 1rem;\n        }\n\n        .banner-url {\n          color: #3b82f6;\n          font-size: 0.875rem;\n          text-decoration: none;\n          margin-bottom: 1rem;\n          display: block;\n          word-break: break-all;\n        }\n\n        .banner-url:hover {\n          text-decoration: underline;\n        }\n\n        .banner-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem 1rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-edit:hover {\n          background: #2563eb;\n        }\n\n        .btn-toggle {\n          background: #f59e0b;\n          color: white;\n        }\n\n        .btn-toggle:hover {\n          background: #d97706;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .banner-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 600px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .banner-form {\n          padding: 1.5rem;\n        }\n\n        .form-text {\n          color: #6b7280;\n          font-size: 0.75rem;\n          margin-top: 0.25rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n\n          .banner-actions {\n            flex-wrap: wrap;\n          }\n\n          .btn-icon {\n            flex: 1;\n            min-width: 120px;\n          }\n        }\n      </style>\n    `;\n  }\n\n  bindEvents() {\n    const addBannerBtn = document.getElementById('addBannerBtn');\n    const closeFormBtn = document.getElementById('closeFormBtn');\n    const cancelFormBtn = document.getElementById('cancelFormBtn');\n    const bannerForm = document.getElementById('bannerForm');\n    const modal = document.getElementById('bannerFormModal');\n    const modalBackdrop = modal?.querySelector('.modal-backdrop');\n\n    addBannerBtn?.addEventListener('click', () => this.showBannerForm());\n    closeFormBtn?.addEventListener('click', () => this.hideBannerForm());\n    cancelFormBtn?.addEventListener('click', () => this.hideBannerForm());\n    modalBackdrop?.addEventListener('click', () => this.hideBannerForm());\n    bannerForm?.addEventListener('submit', (e) => this.handleFormSubmit(e));\n  }\n\n  async loadBanners() {\n    try {\n      const response = await this.apiService.get('/banners');\n      this.banners = response.data || [];\n      this.renderBannersList();\n    } catch (error) {\n      console.error('Error loading banners:', error);\n      this.notificationService.error('Error', 'Failed to load banners');\n      this.renderError();\n    }\n  }\n\n  renderBannersList() {\n    const container = document.getElementById('bannersList');\n    if (!container) return;\n\n    if (this.banners.length === 0) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-image\"></i>\n          <h3>No Banners Found</h3>\n          <p>Start by adding your first banner to promote content on your website.</p>\n        </div>\n      `;\n      return;\n    }\n\n    const cardsHTML = this.banners.map(banner => `\n      <div class=\"banner-card\">\n        <div class=\"banner-preview\">\n          ${banner.banner_image_url ? \n            `<img src=\"${banner.banner_image_url}\" alt=\"${this.escapeHtml(banner.title)}\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">\n             <div class=\"placeholder\" style=\"display: none;\"><i class=\"fas fa-image\"></i></div>` :\n            `<div class=\"placeholder\"><i class=\"fas fa-image\"></i></div>`\n          }\n          <div class=\"banner-status ${banner.active ? 'active' : 'inactive'}\">\n            ${banner.active ? 'Active' : 'Inactive'}\n          </div>\n        </div>\n        <div class=\"banner-content\">\n          <h3 class=\"banner-title\">${this.escapeHtml(banner.title)}</h3>\n          <div class=\"banner-meta\">\n            Created: ${new Date(banner.created_at).toLocaleDateString()}\n          </div>\n          ${banner.redirect_url ? \n            `<a href=\"${banner.redirect_url}\" target=\"_blank\" class=\"banner-url\">${banner.redirect_url}</a>` : \n            ''\n          }\n          <div class=\"banner-actions\">\n            <button class=\"btn-icon btn-edit\" onclick=\"window.bannersPage.editBanner(${banner.id})\">\n              <i class=\"fas fa-edit\"></i>\n              Edit\n            </button>\n            <button class=\"btn-icon btn-toggle\" onclick=\"window.bannersPage.toggleBanner(${banner.id})\">\n              <i class=\"fas fa-${banner.active ? 'eye-slash' : 'eye'}\"></i>\n              ${banner.active ? 'Deactivate' : 'Activate'}\n            </button>\n            <button class=\"btn-icon btn-delete\" onclick=\"window.bannersPage.deleteBanner(${banner.id})\">\n              <i class=\"fas fa-trash\"></i>\n              Delete\n            </button>\n          </div>\n        </div>\n      </div>\n    `).join('');\n\n    container.innerHTML = cardsHTML;\n    \n    // Make this instance globally accessible for onclick handlers\n    window.bannersPage = this;\n  }\n\n  renderError() {\n    const container = document.getElementById('bannersList');\n    if (container) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <h3>Error Loading Banners</h3>\n          <p>There was an error loading the banners. Please try again.</p>\n          <button class=\"btn btn-primary\" onclick=\"window.location.reload()\">Retry</button>\n        </div>\n      `;\n    }\n  }\n\n  showBannerForm(banner = null) {\n    this.editingBanner = banner;\n    const modal = document.getElementById('bannerFormModal');\n    const form = document.getElementById('bannerForm');\n    const title = document.getElementById('formTitle');\n\n    if (banner) {\n      title.textContent = 'Edit Banner';\n      this.populateForm(banner);\n    } else {\n      title.textContent = 'Add New Banner';\n      form.reset();\n      document.getElementById('bannerActive').checked = true;\n    }\n\n    modal.style.display = 'flex';\n    document.body.style.overflow = 'hidden';\n  }\n\n  hideBannerForm() {\n    const modal = document.getElementById('bannerFormModal');\n    modal.style.display = 'none';\n    document.body.style.overflow = '';\n    this.editingBanner = null;\n  }\n\n  populateForm(banner) {\n    document.getElementById('bannerTitle').value = banner.title || '';\n    document.getElementById('bannerImage').value = banner.banner_image_url || '';\n    document.getElementById('bannerRedirect').value = banner.redirect_url || '';\n    document.getElementById('bannerActive').checked = banner.active;\n  }\n\n  async handleFormSubmit(e) {\n    e.preventDefault();\n    \n    const saveBtn = document.getElementById('saveBannerBtn');\n    const btnText = saveBtn.querySelector('.btn-text');\n    const btnLoading = saveBtn.querySelector('.btn-loading');\n    \n    const formData = new FormData(e.target);\n    const bannerData = {\n      title: formData.get('title'),\n      banner_image_url: formData.get('banner_image_url'),\n      redirect_url: formData.get('redirect_url') || null,\n      active: formData.has('active')\n    };\n\n    saveBtn.disabled = true;\n    btnText.classList.add('d-none');\n    btnLoading.classList.remove('d-none');\n\n    try {\n      if (this.editingBanner) {\n        await this.apiService.put(`/banners/${this.editingBanner.id}`, bannerData);\n        this.notificationService.success('Success', 'Banner updated successfully');\n      } else {\n        await this.apiService.post('/banners', bannerData);\n        this.notificationService.success('Success', 'Banner created successfully');\n      }\n\n      this.hideBannerForm();\n      await this.loadBanners();\n\n    } catch (error) {\n      console.error('Error saving banner:', error);\n      this.notificationService.error('Error', error.message || 'Failed to save banner');\n    } finally {\n      saveBtn.disabled = false;\n      btnText.classList.remove('d-none');\n      btnLoading.classList.add('d-none');\n    }\n  }\n\n  editBanner(id) {\n    const banner = this.banners.find(b => b.id === id);\n    if (banner) {\n      this.showBannerForm(banner);\n    }\n  }\n\n  async toggleBanner(id) {\n    const banner = this.banners.find(b => b.id === id);\n    if (!banner) return;\n\n    try {\n      await this.apiService.put(`/banners/${id}`, {\n        ...banner,\n        active: !banner.active\n      });\n      \n      this.notificationService.success('Success', `Banner ${banner.active ? 'deactivated' : 'activated'} successfully`);\n      await this.loadBanners();\n    } catch (error) {\n      console.error('Error toggling banner:', error);\n      this.notificationService.error('Error', error.message || 'Failed to toggle banner status');\n    }\n  }\n\n  async deleteBanner(id) {\n    const banner = this.banners.find(b => b.id === id);\n    if (!banner) return;\n\n    if (!confirm(`Are you sure you want to delete \"${banner.title}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      await this.apiService.delete(`/banners/${id}`);\n      this.notificationService.success('Success', 'Banner deleted successfully');\n      await this.loadBanners();\n    } catch (error) {\n      console.error('Error deleting banner:', error);\n      this.notificationService.error('Error', error.message || 'Failed to delete banner');\n    }\n  }\n\n  escapeHtml(text) {\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n}\n\nexport default BannersPage;\n", "import { ApiService } from '../../lib/ApiService.js';\nimport { NotificationService } from '../../components/NotificationService.js';\n\nclass AdminManagementPage {\n  constructor() {\n    this.apiService = new ApiService();\n    this.notificationService = new NotificationService();\n    this.admins = [];\n    this.showForm = false;\n    this.editingAdmin = null;\n  }\n\n  async render(container) {\n    container.innerHTML = this.getHTML();\n    this.bindEvents();\n    await this.loadAdmins();\n  }\n\n  getHTML() {\n    return `\n      <div class=\"admin-management-page\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Admin Users Management</h1>\n            <p>Manage administrator accounts and permissions</p>\n          </div>\n          <button class=\"btn btn-primary\" id=\"addAdminBtn\">\n            <i class=\"fas fa-plus\"></i>\n            Add Admin User\n          </button>\n        </div>\n\n        <div class=\"admin-content\">\n          <div class=\"admin-list\" id=\"adminList\">\n            <div class=\"loading-state\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Loading admin users...\n            </div>\n          </div>\n\n          <div class=\"admin-form-modal\" id=\"adminFormModal\" style=\"display: none;\">\n            <div class=\"modal-backdrop\"></div>\n            <div class=\"modal-content\">\n              <div class=\"modal-header\">\n                <h3 id=\"formTitle\">Add New Admin User</h3>\n                <button class=\"modal-close\" id=\"closeFormBtn\">\n                  <i class=\"fas fa-times\"></i>\n                </button>\n              </div>\n              <form class=\"admin-form\" id=\"adminForm\">\n                <div class=\"form-group\">\n                  <label for=\"adminUsername\" class=\"form-label\">Username *</label>\n                  <input type=\"text\" id=\"adminUsername\" name=\"username\" class=\"form-control\" required>\n                  <small class=\"form-text\">Username must be unique and contain only letters, numbers, and underscores</small>\n                </div>\n                \n                <div class=\"form-group\">\n                  <label for=\"adminPassword\" class=\"form-label\">Password *</label>\n                  <div class=\"password-input-container\">\n                    <input type=\"password\" id=\"adminPassword\" name=\"password\" class=\"form-control password-input\" required>\n                    <button type=\"button\" class=\"password-toggle\" id=\"passwordToggle\">\n                      <i class=\"fas fa-eye\" id=\"passwordToggleIcon\"></i>\n                    </button>\n                  </div>\n                  <small class=\"form-text\">Password must be at least 8 characters long</small>\n                </div>\n                \n                <div class=\"form-group\">\n                  <label for=\"adminPasswordConfirm\" class=\"form-label\">Confirm Password *</label>\n                  <input type=\"password\" id=\"adminPasswordConfirm\" name=\"passwordConfirm\" class=\"form-control\" required>\n                </div>\n\n                <div class=\"form-actions\">\n                  <button type=\"button\" class=\"btn btn-secondary\" id=\"cancelFormBtn\">Cancel</button>\n                  <button type=\"submit\" class=\"btn btn-primary\" id=\"saveAdminBtn\">\n                    <span class=\"btn-text\">Save Admin User</span>\n                    <span class=\"btn-loading d-none\">\n                      <i class=\"fas fa-spinner fa-spin\"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .admin-management-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .admin-list {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n        }\n\n        .loading-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .admin-table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        .admin-table th,\n        .admin-table td {\n          padding: 1rem;\n          text-align: left;\n          border-bottom: 1px solid #f1f5f9;\n        }\n\n        .admin-table th {\n          background: #f8fafc;\n          font-weight: 600;\n          color: #374151;\n        }\n\n        .admin-table tr:hover {\n          background: #f9fafb;\n        }\n\n        .admin-avatar {\n          width: 40px;\n          height: 40px;\n          background: #3b82f6;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: white;\n          font-weight: 600;\n          margin-right: 1rem;\n        }\n\n        .admin-info {\n          display: flex;\n          align-items: center;\n        }\n\n        .admin-details h4 {\n          margin: 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .admin-details p {\n          margin: 0;\n          font-size: 0.875rem;\n          color: #64748b;\n        }\n\n        .admin-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .btn-delete:disabled {\n          background: #d1d5db;\n          cursor: not-allowed;\n        }\n\n        .admin-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 500px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .admin-form {\n          padding: 1.5rem;\n        }\n\n        .password-input-container {\n          position: relative;\n        }\n\n        .password-input {\n          padding-right: 3rem;\n        }\n\n        .password-toggle {\n          position: absolute;\n          right: 0.75rem;\n          top: 50%;\n          transform: translateY(-50%);\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.25rem;\n          border-radius: 0.25rem;\n          transition: color 0.2s;\n        }\n\n        .password-toggle:hover {\n          color: #374151;\n        }\n\n        .form-text {\n          color: #6b7280;\n          font-size: 0.75rem;\n          margin-top: 0.25rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        .current-user {\n          background: #fef3c7;\n          color: #92400e;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n\n          .admin-table {\n            font-size: 0.875rem;\n          }\n\n          .admin-table th,\n          .admin-table td {\n            padding: 0.75rem 0.5rem;\n          }\n\n          .admin-info {\n            flex-direction: column;\n            align-items: flex-start;\n          }\n\n          .admin-avatar {\n            margin-right: 0;\n            margin-bottom: 0.5rem;\n          }\n        }\n      </style>\n    `;\n  }\n\n  bindEvents() {\n    const addAdminBtn = document.getElementById('addAdminBtn');\n    const closeFormBtn = document.getElementById('closeFormBtn');\n    const cancelFormBtn = document.getElementById('cancelFormBtn');\n    const adminForm = document.getElementById('adminForm');\n    const modal = document.getElementById('adminFormModal');\n    const modalBackdrop = modal?.querySelector('.modal-backdrop');\n    const passwordToggle = document.getElementById('passwordToggle');\n    const passwordInput = document.getElementById('adminPassword');\n    const passwordToggleIcon = document.getElementById('passwordToggleIcon');\n\n    addAdminBtn?.addEventListener('click', () => this.showAdminForm());\n    closeFormBtn?.addEventListener('click', () => this.hideAdminForm());\n    cancelFormBtn?.addEventListener('click', () => this.hideAdminForm());\n    modalBackdrop?.addEventListener('click', () => this.hideAdminForm());\n    adminForm?.addEventListener('submit', (e) => this.handleFormSubmit(e));\n\n    // Password visibility toggle\n    passwordToggle?.addEventListener('click', () => {\n      const isPassword = passwordInput.type === 'password';\n      passwordInput.type = isPassword ? 'text' : 'password';\n      passwordToggleIcon.className = isPassword ? 'fas fa-eye-slash' : 'fas fa-eye';\n    });\n  }\n\n  async loadAdmins() {\n    try {\n      const response = await this.apiService.get('/admins');\n      this.admins = response.data || [];\n      this.renderAdminList();\n    } catch (error) {\n      console.error('Error loading admins:', error);\n      this.notificationService.error('Error', 'Failed to load admin users');\n      this.renderError();\n    }\n  }\n\n  renderAdminList() {\n    const container = document.getElementById('adminList');\n    if (!container) return;\n\n    if (this.admins.length === 0) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-users-cog\"></i>\n          <h3>No Admin Users Found</h3>\n          <p>Start by adding your first admin user to manage the system.</p>\n        </div>\n      `;\n      return;\n    }\n\n    const currentUser = this.getCurrentUser();\n    const tableHTML = `\n      <table class=\"admin-table\">\n        <thead>\n          <tr>\n            <th>Admin User</th>\n            <th>Created</th>\n            <th>Last Updated</th>\n            <th>Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${this.admins.map(admin => `\n            <tr class=\"${currentUser && currentUser.username === admin.username ? 'current-user' : ''}\">\n              <td>\n                <div class=\"admin-info\">\n                  <div class=\"admin-avatar\">\n                    ${admin.username.charAt(0).toUpperCase()}\n                  </div>\n                  <div class=\"admin-details\">\n                    <h4>${this.escapeHtml(admin.username)}</h4>\n                    <p>${currentUser && currentUser.username === admin.username ? 'Current User' : 'Administrator'}</p>\n                  </div>\n                </div>\n              </td>\n              <td>${new Date(admin.created_at).toLocaleDateString()}</td>\n              <td>${new Date(admin.updated_at).toLocaleDateString()}</td>\n              <td>\n                <div class=\"admin-actions\">\n                  <button \n                    class=\"btn-icon btn-delete\" \n                    onclick=\"window.adminManagementPage.deleteAdmin(${admin.id})\"\n                    ${currentUser && currentUser.username === admin.username ? 'disabled title=\"Cannot delete current user\"' : 'title=\"Delete Admin\"'}\n                  >\n                    <i class=\"fas fa-trash\"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n    `;\n\n    container.innerHTML = tableHTML;\n    \n    // Make this instance globally accessible for onclick handlers\n    window.adminManagementPage = this;\n  }\n\n  renderError() {\n    const container = document.getElementById('adminList');\n    if (container) {\n      container.innerHTML = `\n        <div class=\"empty-state\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <h3>Error Loading Admin Users</h3>\n          <p>There was an error loading the admin users. Please try again.</p>\n          <button class=\"btn btn-primary\" onclick=\"window.location.reload()\">Retry</button>\n        </div>\n      `;\n    }\n  }\n\n  showAdminForm() {\n    const modal = document.getElementById('adminFormModal');\n    const form = document.getElementById('adminForm');\n    \n    form.reset();\n    modal.style.display = 'flex';\n    document.body.style.overflow = 'hidden';\n  }\n\n  hideAdminForm() {\n    const modal = document.getElementById('adminFormModal');\n    modal.style.display = 'none';\n    document.body.style.overflow = '';\n  }\n\n  async handleFormSubmit(e) {\n    e.preventDefault();\n    \n    const saveBtn = document.getElementById('saveAdminBtn');\n    const btnText = saveBtn.querySelector('.btn-text');\n    const btnLoading = saveBtn.querySelector('.btn-loading');\n    \n    const formData = new FormData(e.target);\n    const username = formData.get('username');\n    const password = formData.get('password');\n    const passwordConfirm = formData.get('passwordConfirm');\n\n    // Validation\n    if (password !== passwordConfirm) {\n      this.notificationService.error('Validation Error', 'Passwords do not match');\n      return;\n    }\n\n    if (password.length < 8) {\n      this.notificationService.error('Validation Error', 'Password must be at least 8 characters long');\n      return;\n    }\n\n    const adminData = {\n      username,\n      password\n    };\n\n    saveBtn.disabled = true;\n    btnText.classList.add('d-none');\n    btnLoading.classList.remove('d-none');\n\n    try {\n      await this.apiService.post('/admins', adminData);\n      this.notificationService.success('Success', 'Admin user created successfully');\n      this.hideAdminForm();\n      await this.loadAdmins();\n\n    } catch (error) {\n      console.error('Error saving admin:', error);\n      this.notificationService.error('Error', error.message || 'Failed to create admin user');\n    } finally {\n      saveBtn.disabled = false;\n      btnText.classList.remove('d-none');\n      btnLoading.classList.add('d-none');\n    }\n  }\n\n  async deleteAdmin(id) {\n    const admin = this.admins.find(a => a.id === id);\n    if (!admin) return;\n\n    const currentUser = this.getCurrentUser();\n    if (currentUser && currentUser.username === admin.username) {\n      this.notificationService.error('Error', 'Cannot delete your own admin account');\n      return;\n    }\n\n    if (!confirm(`Are you sure you want to delete admin user \"${admin.username}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      await this.apiService.delete(`/admins/${id}`);\n      this.notificationService.success('Success', 'Admin user deleted successfully');\n      await this.loadAdmins();\n    } catch (error) {\n      console.error('Error deleting admin:', error);\n      this.notificationService.error('Error', error.message || 'Failed to delete admin user');\n    }\n  }\n\n  getCurrentUser() {\n    // This would typically come from the auth service\n    // For now, we'll try to get it from the auth service if available\n    if (window.authService && window.authService.getUser) {\n      return window.authService.getUser();\n    }\n    return null;\n  }\n\n  escapeHtml(text) {\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n}\n\nexport default AdminManagementPage;\n", "import LoginPage from '../login/LoginPage.js';\nimport AdminDashboard from '../admin/AdminDashboard.js';\nimport DashboardPage from '../admin/pages/DashboardPage.js';\nimport ProductsPage from '../admin/pages/ProductsPage.js';\nimport BrandsPage from '../admin/pages/BrandsPage.js';\nimport CategoriesPage from '../admin/pages/CategoriesPage.js';\nimport BannersPage from '../admin/pages/BannersPage.js';\nimport AdminManagementPage from '../admin/pages/AdminManagementPage.js';\nimport { AuthService } from '../lib/AuthService.js';\n\nclass Router {\n  constructor() {\n    this.routes = new Map();\n    this.container = null;\n    this.currentRoute = null;\n    this.authService = new AuthService();\n    this.setupRoutes();\n  }\n\n  setupRoutes() {\n    // Public routes\n    this.routes.set('/', { component: LoginPage, requiresAuth: false });\n    this.routes.set('/login', { component: LoginPage, requiresAuth: false });\n    \n    // Admin routes (protected)\n    this.routes.set('/admin', { component: AdminDashboard, requiresAuth: true, defaultChild: '/admin/dashboard' });\n    this.routes.set('/admin/dashboard', { component: DashboardPage, requiresAuth: true, parent: '/admin' });\n    this.routes.set('/admin/products', { component: ProductsPage, requiresAuth: true, parent: '/admin' });\n    this.routes.set('/admin/brands', { component: BrandsPage, requiresAuth: true, parent: '/admin' });\n    this.routes.set('/admin/categories', { component: CategoriesPage, requiresAuth: true, parent: '/admin' });\n    this.routes.set('/admin/banners', { component: BannersPage, requiresAuth: true, parent: '/admin' });\n    this.routes.set('/admin/admins', { component: AdminManagementPage, requiresAuth: true, parent: '/admin' });\n  }\n\n  init(container) {\n    this.container = container;\n    \n    // Handle browser navigation\n    window.addEventListener('popstate', () => {\n      this.handleRoute();\n    });\n\n    // Handle initial route\n    this.handleRoute();\n  }\n\n  navigate(path, replace = false) {\n    if (replace) {\n      window.history.replaceState({}, '', path);\n    } else {\n      window.history.pushState({}, '', path);\n    }\n    this.handleRoute();\n  }\n\n  async handleRoute() {\n    const path = window.location.pathname;\n    let route = this.routes.get(path);\n    \n    // If no exact match, try to find parent route\n    if (!route) {\n      for (const [routePath, routeConfig] of this.routes) {\n        if (path.startsWith(routePath) && routeConfig.defaultChild) {\n          route = routeConfig;\n          break;\n        }\n      }\n    }\n\n    // Default to login if no route found\n    if (!route) {\n      this.navigate('/login', true);\n      return;\n    }\n\n    // Check authentication\n    const isAuthenticated = await this.authService.checkAuth();\n    \n    if (route.requiresAuth && !isAuthenticated) {\n      this.navigate('/login', true);\n      return;\n    }\n\n    if (!route.requiresAuth && isAuthenticated && path === '/login') {\n      this.navigate('/admin/dashboard', true);\n      return;\n    }\n\n    // Handle default child routes\n    if (route.defaultChild && path === Object.keys(Object.fromEntries(this.routes)).find(key => this.routes.get(key) === route)) {\n      this.navigate(route.defaultChild, true);\n      return;\n    }\n\n    // Render the route\n    await this.renderRoute(route, path);\n  }\n\n  async renderRoute(route, path) {\n    if (!this.container) return;\n\n    try {\n      // Clear container\n      this.container.innerHTML = '';\n      \n      // Create component instance\n      const ComponentClass = route.component;\n      const component = new ComponentClass();\n      \n      // Handle parent-child relationship for admin routes\n      if (route.parent) {\n        const parentRoute = this.routes.get(route.parent);\n        if (parentRoute) {\n          const parentComponent = new parentRoute.component();\n          await parentComponent.render(this.container);\n          \n          // Find the content area in the parent component\n          const contentArea = this.container.querySelector('.admin-content');\n          if (contentArea) {\n            await component.render(contentArea);\n          } else {\n            await component.render(this.container);\n          }\n        } else {\n          await component.render(this.container);\n        }\n      } else {\n        await component.render(this.container);\n      }\n      \n      this.currentRoute = { route, path, component };\n      \n    } catch (error) {\n      console.error('Error rendering route:', error);\n      this.container.innerHTML = `\n        <div style=\"padding: 2rem; text-align: center;\">\n          <h2>Error Loading Page</h2>\n          <p>There was an error loading this page. Please try again.</p>\n          <button onclick=\"window.location.reload()\" class=\"btn btn-primary\">Reload Page</button>\n        </div>\n      `;\n    }\n  }\n\n  getCurrentRoute() {\n    return this.currentRoute;\n  }\n}\n\nexport default Router;\n", "import Router from './route/Router.js';\r\nimport { AuthService } from './lib/AuthService.js';\r\nimport { NotificationService } from './components/NotificationService.js';\r\n\r\nclass App {\r\n  constructor() {\r\n    this.router = new Router();\r\n    this.authService = new AuthService();\r\n    this.notificationService = new NotificationService();\r\n    this.init();\r\n  }\r\n\r\n  init() {\r\n    // Initialize notification service\r\n    this.notificationService.init();\r\n\r\n    // Set up global error handling\r\n    window.addEventListener('unhandledrejection', (event) => {\r\n      console.error('Unhandled promise rejection:', event.reason);\r\n      this.notificationService.error('An unexpected error occurred');\r\n    });\r\n\r\n    // Set up auth state change listener\r\n    this.authService.onAuthStateChange((isAuthenticated) => {\r\n      if (!isAuthenticated && window.location.pathname.startsWith('/admin')) {\r\n        this.router.navigate('/login');\r\n      }\r\n    });\r\n  }\r\n\r\n  mount(container) {\r\n    this.container = container;\r\n    this.router.init(container);\r\n  }\r\n}\r\n\r\nexport default App;", "import './styles/global.css';\r\nimport App from './App.js';\r\n\r\n// Initialize the application\r\ndocument.addEventListener('DOMContentLoaded', () => {\r\n  const root = document.getElementById('root');\r\n  if (root) {\r\n    root.innerHTML = '';\r\n    const app = new App();\r\n    app.mount(root);\r\n  }\r\n});", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t792: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkggcasecatalogs\"] = self[\"webpackChunkggcasecatalogs\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [96], () => (__webpack_require__(744)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["deferred", "___CSS_LOADER_EXPORT___", "push", "module", "id", "options", "styleTagTransform", "setAttributes", "insert", "domAPI", "insertStyleElement", "locals", "ApiService", "_createClass", "_classCallCheck", "this", "baseURL", "defaultHeaders", "key", "value", "_request", "_asyncToGenerator", "_regeneratorRuntime", "_callee", "endpoint", "url", "config", "response", "contentType", "data", "_args", "arguments", "_context", "prev", "next", "length", "undefined", "concat", "_objectSpread", "headers", "credentials", "fetch", "sent", "get", "includes", "json", "text", "ok", "_typeof", "message", "Error", "status", "statusText", "abrupt", "stop", "_x", "apply", "_get", "_callee2", "queryString", "_args2", "_context2", "URLSearchParams", "toString", "request", "method", "_x2", "_post", "_callee3", "_args3", "_context3", "body", "JSON", "stringify", "_x3", "_put", "_callee4", "_args4", "_context4", "_x4", "_delete2", "_callee5", "_context5", "_x5", "_uploadFile", "_callee6", "formData", "_context6", "_x6", "_x7", "_getAll", "_callee7", "resource", "_context7", "_x8", "_getById", "_callee8", "_context8", "_x9", "_x0", "_create", "_callee9", "_context9", "post", "_x1", "_x10", "_update", "_callee0", "_context0", "put", "_x11", "_x12", "_x13", "_remove", "_callee1", "_context1", "_x14", "_x15", "AuthService", "apiService", "isAuthenticated", "user", "authStateListeners", "loginAttempts", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutTime", "lockoutUntil", "_checkAuth", "success", "authenticated", "notifyAuthStateChange", "_login", "username", "password", "remainingTime", "_t2", "isAccountLocked", "Math", "ceil", "Date", "now", "handleFailedLogin", "_logout", "finish", "localStorage", "setItem", "storedLockout", "getItem", "parseInt", "removeItem", "max", "callback", "_this", "index", "indexOf", "splice", "_this2", "for<PERSON>ach", "error", "NotificationService", "container", "notifications", "nextId", "document", "getElementById", "addStyles", "style", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "type", "title", "duration", "notification", "render", "setTimeout", "remove", "show", "findIndex", "n", "notificationContainer", "querySelector", "className", "innerHTML", "element", "createNotificationElement", "classList", "add", "_this3", "dataset", "icon", "getIcon", "escapeHtml", "addEventListener", "icons", "warning", "info", "div", "LoginPage", "authService", "notificationService", "showPassword", "isLoading", "_render", "getHTML", "bindEvents", "updateLoginAttempts", "form", "passwordToggle", "passwordInput", "passwordToggleIcon", "e", "preventDefault", "handleLogin", "_handleLogin", "submitBtn", "submitText", "submitLoading", "usernameInput", "_t", "trim", "disabled", "login", "window", "location", "href", "focus", "<PERSON><PERSON><PERSON><PERSON>", "remainingAttempts", "getRemainingAttempts", "isLocked", "getLockoutTimeRemaining", "minutes", "AdminDashboard", "sidebarCollapsed", "loadUserInfo", "sidebarToggle", "mobileMenuToggle", "userMenuToggle", "userMenuDropdown", "logoutBtn", "sidebar", "toggle", "stopPropagation", "_ref", "handleLogout", "querySelectorAll", "link", "route", "navigateToRoute", "updateActiveNavLink", "_loadUserInfo", "userNameElement", "getUser", "_handleLogout", "logout", "pageTitle", "history", "pushState", "dispatchEvent", "PopStateEvent", "currentPath", "pathname", "DashboardPage", "stats", "products", "brands", "categories", "banners", "loadStats", "_loadStats", "_products$data", "_brands$data", "_categories$data", "_banners$data", "_yield$Promise$all", "_yield$Promise$all2", "Promise", "all", "_slicedToArray", "updateStatsDisplay", "testDatabaseConnection", "elements", "productsCount", "brandsCount", "categoriesCount", "bannersCount", "_testDatabaseConnection", "db<PERSON><PERSON>us", "_dbStatus", "ProductsPage", "showForm", "editingProduct", "loadData", "addProductBtn", "closeFormBtn", "cancelFormBtn", "productForm", "modal", "modalBackdrop", "showProductForm", "hideProductForm", "handleFormSubmit", "_loadData", "productsResponse", "brandsResponse", "categoriesResponse", "renderProductsList", "populateFormSelects", "renderError", "tableHTML", "map", "product", "name", "description", "substring", "brand_name", "category_name", "created_at", "toLocaleDateString", "join", "productsPage", "brandSelect", "categorySelect", "brand", "category", "populateForm", "reset", "display", "overflow", "brand_id", "category_id", "_handleFormSubmit", "saveBtn", "btnText", "btnLoading", "productData", "FormData", "target", "find", "p", "_deleteProduct", "_t3", "confirm", "BrandsPage", "<PERSON><PERSON><PERSON>", "loadBrands", "addBrandBtn", "brandForm", "showBrandForm", "hideBrandForm", "_loadBrands", "renderBrandsGrid", "cardsHTML", "brand_photo", "brandsPage", "brandData", "b", "_delete<PERSON>rand", "CategoriesPage", "editingCategory", "loadCategories", "addCategoryBtn", "categoryForm", "showCategoryForm", "hideCategoryForm", "_loadCategories", "renderCategoriesGrid", "category_photo", "categoriesPage", "categoryData", "c", "_deleteCategory", "BannersPage", "editingBanner", "loadBanners", "addBannerBtn", "bannerForm", "showBannerForm", "hideBannerForm", "_loadBanners", "renderBannersList", "banner", "banner_image_url", "active", "redirect_url", "bannersPage", "checked", "bannerData", "has", "_toggleBanner", "_deleteBanner", "_t4", "AdminManagementPage", "admins", "editingAdmin", "loadAdmins", "addAdminBtn", "adminForm", "showAdminForm", "hideAdminForm", "isPassword", "_loadAdmins", "renderAdminList", "currentUser", "getCurrentUser", "admin", "char<PERSON>t", "toUpperCase", "updated_at", "adminManagementPage", "passwordConfirm", "adminData", "_deleteAdmin", "a", "Router", "routes", "Map", "currentRoute", "setupRoutes", "set", "component", "requiresAuth", "defaultChild", "parent", "handleRoute", "path", "replaceState", "_handleRoute", "_iterator", "_step", "_step$value", "routePath", "routeConfig", "_createForOfIteratorHelper", "s", "done", "startsWith", "f", "navigate", "checkAuth", "Object", "keys", "fromEntries", "renderRoute", "_renderRoute", "ComponentClass", "parentRoute", "parentComponent", "contentArea", "App", "router", "init", "event", "onAuthStateChange", "root", "mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "__webpack_modules__", "m", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "every", "r", "getter", "__esModule", "d", "definition", "o", "defineProperty", "enumerable", "obj", "prop", "prototype", "hasOwnProperty", "call", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "bind", "nc", "__webpack_exports__"], "sourceRoot": ""}